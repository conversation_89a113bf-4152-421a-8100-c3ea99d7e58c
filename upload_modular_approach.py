"""
Upload Modular Approach to Hugging Face
Uploads both the API wrapper (app.py) and the original script (generate_fen_v6_geometric.py)
"""

import os
import shutil
from huggingface_hub import HfApi, upload_file

def upload_modular_version():
    """Upload the modular version to Hugging Face"""
    
    # Hugging Face credentials
    repo_id = "yamero999/chess-fen-generation-api"
    
    try:
        # Initialize HF API
        api = HfApi()
        
        print("🚀 Uploading modular approach to Hugging Face...")
        
        # Upload the API wrapper as app.py
        print("📤 Uploading app_module.py as app.py...")
        upload_file(
            path_or_fileobj="app_module.py",
            path_in_repo="app.py",
            repo_id=repo_id,
            repo_type="space",
            commit_message="🔧 MODULAR APPROACH: API wrapper that imports generate_fen_v6_geometric.py as module"
        )
        
        # Upload the original script
        print("📤 Uploading generate_fen_v6_geometric.py...")
        upload_file(
            path_or_fileobj="generate_fen_v6_geometric.py",
            path_in_repo="generate_fen_v6_geometric.py",
            repo_id=repo_id,
            repo_type="space",
            commit_message="📜 ORIGINAL SCRIPT: Your original generate_fen_v6_geometric.py script"
        )
        
        # Upload the V6 model architecture file if it exists
        v6_model_file = "breakthrough_unet_v6_simple.py"
        if os.path.exists(v6_model_file):
            print("📤 Uploading V6 model architecture...")
            upload_file(
                path_or_fileobj=v6_model_file,
                path_in_repo=v6_model_file,
                repo_id=repo_id,
                repo_type="space",
                commit_message="🏗️ V6 ARCHITECTURE: V6 model architecture file"
            )
        else:
            print("⚠️ V6 model architecture file not found - you'll need to upload it manually")
        
        print("✅ Modular version uploaded successfully!")
        print(f"🌐 URL: https://huggingface.co/spaces/{repo_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Upload failed: {e}")
        return False

def create_requirements_txt():
    """Create requirements.txt for the Hugging Face space"""
    requirements = """torch
torchvision
ultralytics
opencv-python
numpy
gradio
Pillow
huggingface-hub
matplotlib
"""
    
    with open("requirements.txt", "w") as f:
        f.write(requirements)
    
    print("📝 Created requirements.txt")

def create_readme():
    """Create README.md for the Hugging Face space"""
    readme_content = """---
title: Chess FEN Generator
emoji: ♟️
colorFrom: blue
colorTo: green
sdk: gradio
sdk_version: 4.0.0
app_file: app.py
pinned: false
license: mit
---

# ♟️ Chess FEN Generator

Upload a chess board image to generate FEN notation using advanced computer vision.

## Architecture

This application uses a modular approach:

1. **`app.py`** - API wrapper with Gradio interface
2. **`generate_fen_v6_geometric.py`** - Original FEN generation script
3. **V6 Model** - Board segmentation and detection
4. **YOLO Model** - Chess piece detection

## Processing Pipeline

1. **V6 Board Detection** (256x256) - Segments chess board from image
2. **Perspective Correction** (512x512) - Creates perfect square board
3. **Dual YOLO Detection** (416x416) - Detects pieces on original + enhanced board
4. **Grid Mapping** - Maps detected pieces to chess squares
5. **FEN Generation** - Converts grid to standard FEN notation

## Model Files Required

- `best.pt` - V6 board segmentation model
- `best_mobile.onnx` - YOLO piece detection model

## Usage

Simply upload a chess board image and get the FEN notation instantly!
"""
    
    with open("README.md", "w") as f:
        f.write(readme_content)
    
    print("📝 Created README.md")

if __name__ == "__main__":
    print("🔧 MODULAR APPROACH DEPLOYMENT")
    print("=" * 50)
    
    # Create supporting files
    create_requirements_txt()
    create_readme()
    
    # Upload everything
    success = upload_modular_version()
    
    if success:
        print("\n🎯 MODULAR DEPLOYMENT COMPLETE!")
        print("\nThis approach:")
        print("✅ Keeps your original script separate and unchanged")
        print("✅ Uses a clean API wrapper (app.py)")
        print("✅ Imports your script as a module")
        print("✅ Preserves all original functionality")
        print("✅ Easy to maintain and update")
        print("\n📋 Next steps:")
        print("1. Upload model files (best.pt, best_mobile.onnx) to the space")
        print("2. Upload V6 model architecture file if needed")
        print("3. Test with a chess board image")
        print(f"\n🌐 Visit: https://huggingface.co/spaces/yamero999/chess-fen-generation-api")
    else:
        print("\n❌ Deployment failed!")
