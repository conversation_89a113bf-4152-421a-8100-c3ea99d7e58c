"""
Fix HuggingFace Space - Force upload app.py and requirements.txt
"""

from huggingface_hub import HfApi, upload_file
import os
import time

def create_space_config():
    """Create proper space configuration"""
    config_content = """---
title: V2 ONNX Chess FEN Generator
emoji: ♟️
colorFrom: blue
colorTo: green
sdk: gradio
sdk_version: 5.32.0
app_file: app.py
pinned: false
license: apache-2.0
---

# V2 ONNX Chess FEN Generator

🚀 Breakthrough performance with V2 ONNX model - 4.57x faster chess board segmentation!
"""
    
    with open("README.md", "w", encoding="utf-8") as f:
        f.write(config_content)
    
    print("📝 Created README.md with proper space configuration")

def force_upload_files():
    """Force upload all necessary files"""
    
    space_repo = "yamero999/chess-fen-generation-api"
    hf_token = "*************************************"
    
    print("🚀 Force uploading all files to fix HuggingFace Space...")
    print(f"📁 Target: {space_repo}")
    
    try:
        api = HfApi(token=hf_token)
        
        # Create space config
        create_space_config()
        
        # Upload README.md first
        print("📤 Uploading README.md...")
        upload_file(
            path_or_fileobj="README.md",
            path_in_repo="README.md",
            repo_id=space_repo,
            repo_type="space",
            token=hf_token,
            commit_message="📝 Fix space configuration"
        )
        
        # Upload app.py with timestamp to force update
        print("📤 Force uploading app.py...")
        
        # Add timestamp comment to force update
        with open("app_v2_onnx.py", "r", encoding="utf-8") as f:
            app_content = f.read()
        
        # Add timestamp to force update
        timestamp_comment = f"# Force update timestamp: {int(time.time())}\n"
        app_content_with_timestamp = timestamp_comment + app_content
        
        with open("app_temp.py", "w", encoding="utf-8") as f:
            f.write(app_content_with_timestamp)
        
        upload_file(
            path_or_fileobj="app_temp.py",
            path_in_repo="app.py",
            repo_id=space_repo,
            repo_type="space",
            token=hf_token,
            commit_message="🚀 FORCE: Deploy V2 ONNX app with timestamp"
        )
        
        # Upload requirements.txt
        print("📤 Uploading requirements.txt...")
        upload_file(
            path_or_fileobj="requirements_v2_onnx.txt",
            path_in_repo="requirements.txt",
            repo_id=space_repo,
            repo_type="space",
            token=hf_token,
            commit_message="📦 Update requirements for V2 ONNX"
        )
        
        # Clean up temp files
        if os.path.exists("app_temp.py"):
            os.remove("app_temp.py")
        if os.path.exists("README.md"):
            os.remove("README.md")
        
        print("✅ All files uploaded successfully!")
        print(f"🔗 Space: https://huggingface.co/spaces/{space_repo}")
        print("🔄 Space should restart automatically in ~2-3 minutes")
        print("\n💡 If the issue persists:")
        print("1. Check the space logs for errors")
        print("2. Verify the app.py file is present")
        print("3. Ensure requirements.txt has all dependencies")
        
        return True
        
    except Exception as e:
        print(f"❌ Upload failed: {e}")
        return False

def check_file_contents():
    """Check that our files have the right content"""
    print("🔍 Checking file contents...")
    
    # Check app file
    if os.path.exists("app_v2_onnx.py"):
        with open("app_v2_onnx.py", "r") as f:
            content = f.read()
        
        if "if __name__ == \"__main__\":" in content:
            print("✅ app_v2_onnx.py has main execution block")
        else:
            print("❌ app_v2_onnx.py missing main execution block")
        
        if "demo.launch" in content:
            print("✅ app_v2_onnx.py has Gradio launch")
        else:
            print("❌ app_v2_onnx.py missing Gradio launch")
    else:
        print("❌ app_v2_onnx.py not found")
    
    # Check requirements
    if os.path.exists("requirements_v2_onnx.txt"):
        with open("requirements_v2_onnx.txt", "r") as f:
            content = f.read()
        
        if "gradio" in content:
            print("✅ requirements_v2_onnx.txt has gradio")
        else:
            print("❌ requirements_v2_onnx.txt missing gradio")
        
        if "onnxruntime" in content:
            print("✅ requirements_v2_onnx.txt has onnxruntime")
        else:
            print("❌ requirements_v2_onnx.txt missing onnxruntime")
    else:
        print("❌ requirements_v2_onnx.txt not found")

def main():
    """Main fix function"""
    print("🔧 FIXING HUGGINGFACE SPACE - NO APPLICATION FILE ERROR")
    print("="*60)
    
    # Check files first
    check_file_contents()
    print()
    
    # Force upload
    success = force_upload_files()
    
    if success:
        print("\n🎉 SPACE FIX COMPLETED!")
        print("="*60)
        print("✅ All files uploaded with force update")
        print("✅ Space configuration fixed")
        print("✅ Requirements updated")
        print("\n🔄 Next steps:")
        print("1. Wait 2-3 minutes for space restart")
        print("2. Check space logs if issues persist")
        print("3. Verify app.py is visible in space files")
        print("\n🔗 Your space: https://huggingface.co/spaces/yamero999/chess-fen-generation-api")
    else:
        print("\n❌ SPACE FIX FAILED")
        print("Please check the error messages above")

if __name__ == "__main__":
    main()
