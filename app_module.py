"""
Hugging Face Spaces API for Chess FEN Generation
API wrapper that imports and calls generate_fen_v6_geometric.py as a module
"""

import os
import sys
import tempfile
import time
import json
from pathlib import Path
import cv2
import numpy as np
import gradio as gr
import logging
from PIL import Image

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add current directory to path so we can import the FEN generation script
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import your original script as a module
try:
    import generate_fen_v6_geometric as fen_generator
    logger.info("✅ Successfully imported generate_fen_v6_geometric.py")
    SCRIPT_AVAILABLE = True
except ImportError as e:
    logger.error(f"❌ Failed to import generate_fen_v6_geometric.py: {e}")
    SCRIPT_AVAILABLE = False

def process_chess_image_api(image_file):
    """
    Main API function that calls functions from generate_fen_v6_geometric.py
    """
    if not SCRIPT_AVAILABLE:
        return {
            "success": False,
            "error": "generate_fen_v6_geometric.py not available",
            "fen": None
        }
    
    try:
        start_time = time.time()
        logger.info("🚀 Processing chess image via generate_fen_v6_geometric module")
        
        # Save uploaded image to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as tmp_file:
            image_path = tmp_file.name
            
            # Convert and save image
            if isinstance(image_file, np.ndarray):
                # Convert numpy array to BGR for OpenCV
                image_bgr = cv2.cvtColor(image_file, cv2.COLOR_RGB2BGR)
                cv2.imwrite(image_path, image_bgr)
            elif hasattr(image_file, 'save'):
                # PIL Image
                image_file.save(image_path)
            else:
                # Assume it's already in the right format
                cv2.imwrite(image_path, image_file)
        
        logger.info(f"📁 Saved image to: {image_path}")
        
        # Set up device
        import torch
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"🖥️ Using device: {device}")
        
        # Load models using the original script's functions
        logger.info("🚀 Loading V6 model...")
        v6_model_path = "best.pt"  # V6 model file
        v6_model = fen_generator.load_v6_model(v6_model_path, device)
        
        if v6_model is None:
            return {
                "success": False,
                "error": "Failed to load V6 model",
                "fen": None
            }
        
        # Stage 1: V6 Board Detection
        logger.info("🔍 Stage 1: V6 Board Detection...")
        stage1_start = time.time()
        board_results = fen_generator.detect_chessboard_v6_geometric(v6_model, image_path, device)
        stage1_time = time.time() - stage1_start
        
        if board_results is None:
            return {
                "success": False,
                "error": "Board detection failed",
                "fen": None
            }
        
        # Stage 2: Piece Detection
        logger.info("🎯 Stage 2: Piece Detection...")
        stage2_start = time.time()
        piece_model_path = "best_mobile.onnx"  # YOLO model file
        pieces = fen_generator.detect_pieces(piece_model_path, board_results['board_corrected'])
        stage2_time = time.time() - stage2_start
        
        # Stage 3: Grid Mapping
        logger.info("🗺️ Stage 3: Grid Mapping...")
        stage3_start = time.time()
        grid = fen_generator.map_pieces_to_geometric_grid(pieces, board_results['squares'])
        stage3_time = time.time() - stage3_start
        
        # Stage 4: FEN Generation
        logger.info("📝 Stage 4: FEN Generation...")
        stage4_start = time.time()
        fen = fen_generator.generate_fen(grid)
        stage4_time = time.time() - stage4_start
        
        # Clean up temporary image
        try:
            os.unlink(image_path)
        except:
            pass
        
        total_time = time.time() - start_time
        
        logger.info(f"✅ Processing completed in {total_time:.2f}s")
        logger.info(f"📝 Generated FEN: {fen}")
        
        return {
            "success": True,
            "fen": fen,
            "pieces_detected": len(pieces),
            "pieces_mapped": sum(1 for row in grid for cell in row if cell is not None),
            "processing_time": total_time,
            "stage_times": {
                "board_detection": stage1_time,
                "piece_detection": stage2_time,
                "grid_mapping": stage3_time,
                "fen_generation": stage4_time
            },
            "v6_inference_time": board_results.get('inference_time', 0),
            "method": "generate_fen_v6_geometric module"
        }
        
    except Exception as e:
        logger.error(f"❌ API processing failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        
        # Clean up temporary image
        try:
            os.unlink(image_path)
        except:
            pass
        
        return {
            "success": False,
            "error": str(e),
            "fen": None
        }

def gradio_interface(image):
    """Gradio interface function"""
    if image is None:
        return "Please upload an image."
    
    if not SCRIPT_AVAILABLE:
        return """❌ **Script Not Available**

The `generate_fen_v6_geometric.py` script could not be imported.

Please ensure:
1. `generate_fen_v6_geometric.py` is in the same directory as this app
2. All required dependencies are installed
3. The script has no syntax errors"""
    
    # Process the image
    result = process_chess_image_api(image)
    
    if result["success"]:
        return f"""✅ **Chess FEN Generated Successfully!**

**FEN Notation:** `{result['fen']}`

**Processing Details:**
- 🎯 Pieces Detected: {result['pieces_detected']}
- 🗺️ Pieces Mapped: {result['pieces_mapped']}
- ⏱️ Total Time: {result['processing_time']:.2f}s
- 🔍 V6 Inference: {result['v6_inference_time']:.1f}ms
- 🛠️ Method: {result['method']}

**Stage Breakdown:**
- 🔍 Board Detection: {result['stage_times']['board_detection']:.2f}s
- 🎯 Piece Detection: {result['stage_times']['piece_detection']:.2f}s
- 🗺️ Grid Mapping: {result['stage_times']['grid_mapping']:.3f}s
- 📝 FEN Generation: {result['stage_times']['fen_generation']:.3f}s

**Architecture Used:**
- V6 Board Detection (256x256)
- Perspective Correction (512x512)
- Dual YOLO Detection (416x416)
- Direct Grid Mapping
- FEN Generation

*This imports and calls your original generate_fen_v6_geometric.py script as a module.*"""
    else:
        return f"""❌ **Processing Failed:** {result['error']}

**Debug Information:**
- Error: {result.get('error', 'Unknown error')}
- Processing Time: {result.get('processing_time', 'N/A')}

Please check:
1. Model files (best.pt, best_mobile.onnx) are available
2. Image is a valid chess board
3. All dependencies are properly installed"""

# Create Gradio interface
if SCRIPT_AVAILABLE:
    title = "♟️ Chess FEN Generator (Original Script Module)"
    description = """Upload a chess board image to generate FEN notation.

This API imports and calls your original `generate_fen_v6_geometric.py` script as a module.

**Status:** ✅ Script successfully imported and ready to use."""
else:
    title = "♟️ Chess FEN Generator - Script Not Available"
    description = """⚠️ **Script Import Failed**

The `generate_fen_v6_geometric.py` script could not be imported.

Please ensure the script and all dependencies are properly set up."""

demo = gr.Interface(
    fn=gradio_interface,
    inputs=gr.Image(type="numpy"),
    outputs=gr.Textbox(label="FEN Result", lines=25),
    title=title,
    description=description,
    examples=None
)

if __name__ == "__main__":
    demo.launch()
