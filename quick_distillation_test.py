"""
Quick Knowledge Distillation Test
Fast test of the distillation system with reduced epochs for demonstration
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import os
import time
from torch.utils.data import Dataset, DataLoader

# Import the distillation components
from v6_knowledge_distillation import (
    LightweightStudentV6, 
    ChessBoardDataset, 
    DistillationLoss,
    load_teacher_model,
    create_student_model,
    evaluate_distillation_quality,
    benchmark_models,
    convert_student_to_onnx,
    test_student_onnx
)

def quick_distillation_test(model_path="best_model.pth", num_epochs=10):
    """Quick distillation test with reduced epochs"""
    print("🚀 QUICK V6 KNOWLEDGE DISTILLATION TEST")
    print("=" * 50)
    print(f"📚 Training for {num_epochs} epochs (quick test)")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Using device: {device}")
    
    try:
        # Step 1: Load teacher model
        teacher = load_teacher_model(model_path, device)
        
        # Step 2: Create student model
        student = create_student_model(device, base_channels=8)
        
        # Step 3: Quick distillation training
        print(f"🧠 Starting quick knowledge distillation ({num_epochs} epochs)...")
        
        # Create small dataset for quick test
        dataset = ChessBoardDataset(num_samples=500)  # Reduced from 5000
        dataloader = DataLoader(dataset, batch_size=4, shuffle=True, num_workers=0)
        
        # Setup training
        optimizer = optim.AdamW(student.parameters(), lr=0.001, weight_decay=1e-4)
        criterion = DistillationLoss(temperature=4.0, alpha=0.7, beta=0.2)
        
        teacher.eval()
        student.train()
        
        for epoch in range(num_epochs):
            epoch_loss = 0
            num_batches = 0
            
            for batch_idx, images in enumerate(dataloader):
                images = images.to(device)
                
                # Teacher forward pass (no gradients)
                with torch.no_grad():
                    teacher_output = teacher(images)
                
                # Student forward pass
                student_output = student(images)
                
                # Calculate distillation loss
                total_loss, dist_loss, output_loss, feat_loss = criterion(
                    student_output, teacher_output
                )
                
                # Backward pass
                optimizer.zero_grad()
                total_loss.backward()
                torch.nn.utils.clip_grad_norm_(student.parameters(), max_norm=1.0)
                optimizer.step()
                
                epoch_loss += total_loss.item()
                num_batches += 1
                
                # Progress logging
                if batch_idx % 25 == 0:
                    print(f"  Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}, "
                          f"Loss: {total_loss.item():.4f}")
            
            avg_loss = epoch_loss / num_batches
            print(f"Epoch {epoch+1}/{num_epochs} completed, Avg Loss: {avg_loss:.4f}")
        
        print("✅ Quick distillation completed!")
        
        # Step 4: Evaluate distillation quality
        mse, mae, max_diff = evaluate_distillation_quality(teacher, student, device, num_test_samples=20)
        
        # Step 5: Benchmark performance
        teacher_time, student_time, speedup = benchmark_models(teacher, student, device, num_runs=20)
        
        # Step 6: Save student model
        os.makedirs("quick_test", exist_ok=True)
        student_path = "quick_test/quick_student_v6.pth"
        torch.save(student.state_dict(), student_path)
        
        # Step 7: Convert to ONNX
        onnx_path = "quick_test/quick_student_v6.onnx"
        convert_student_to_onnx(student, onnx_path, device)
        
        # Step 8: Test ONNX
        onnx_success, onnx_time = test_student_onnx(onnx_path, student, device)
        
        # Step 9: Calculate results
        teacher_params = sum(p.numel() for p in teacher.parameters())
        student_params = sum(p.numel() for p in student.parameters())
        
        print("\n🎉 QUICK DISTILLATION TEST RESULTS:")
        print("=" * 50)
        print(f"Teacher V6 parameters: {teacher_params:,}")
        print(f"Student V6 parameters: {student_params:,}")
        print(f"Parameter reduction: {(1 - student_params/teacher_params)*100:.1f}%")
        print()
        print(f"Teacher inference: {teacher_time:.2f} ms")
        print(f"Student PyTorch: {student_time:.2f} ms")
        print(f"Student ONNX: {onnx_time:.2f} ms")
        print(f"PyTorch speedup: {speedup:.2f}x")
        if onnx_time > 0:
            print(f"ONNX speedup: {teacher_time/onnx_time:.2f}x")
        print()
        print(f"Knowledge transfer MSE: {mse:.6f}")
        print(f"Knowledge transfer MAE: {mae:.6f}")
        print(f"Max output difference: {max_diff:.6f}")
        
        # Assessment
        print("\n💡 QUICK TEST ASSESSMENT:")
        if speedup > 5:
            print("🚀 Excellent speedup achieved!")
        elif speedup > 2:
            print("✅ Good speedup achieved")
        else:
            print("⚠️ Limited speedup - may need more training")
        
        if mse < 0.01:
            print("🎉 Good knowledge transfer!")
        else:
            print("📚 Knowledge transfer needs more training epochs")
        
        return {
            'teacher_params': teacher_params,
            'student_params': student_params,
            'parameter_reduction': (1 - student_params/teacher_params)*100,
            'teacher_time': teacher_time,
            'student_time': student_time,
            'onnx_time': onnx_time,
            'pytorch_speedup': speedup,
            'onnx_speedup': teacher_time/onnx_time if onnx_time > 0 else 0,
            'knowledge_mse': mse,
            'knowledge_mae': mae,
            'max_diff': max_diff,
            'student_path': student_path,
            'onnx_path': onnx_path
        }
        
    except Exception as e:
        print(f"❌ Quick test failed: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    # Run quick distillation test
    results = quick_distillation_test(
        model_path="best_model.pth",
        num_epochs=10  # Quick test with 10 epochs
    )
    
    print("\n🎓 QUICK TEST SUMMARY:")
    print("=" * 40)
    for key, value in results.items():
        if isinstance(value, float):
            print(f"{key}: {value:.4f}")
        else:
            print(f"{key}: {value}")
    
    print("\n🚀 Next Steps:")
    print("1. If results look good, run full distillation (100 epochs)")
    print("2. Test on real chess board images")
    print("3. Upload best model to HuggingFace")
    print("4. Update app.py to use distilled model")
