"""
Validate ONNX Model Accuracy
Tests the existing ONNX models to ensure they maintain accuracy from the PyTorch version.
"""

import torch
import numpy as np
import os
import time
import cv2
from ultimate_breakthrough_v2 import UltimateBreakthroughV2

def load_pytorch_model(model_path, device):
    """Load the PyTorch Ultimate V2 model"""
    print("🚀 Loading PyTorch Ultimate V2 model...")
    
    model = UltimateBreakthroughV2()
    model.load_state_dict(torch.load(model_path, map_location=device, weights_only=True))
    model.eval()
    model.to(device)
    
    print("✅ PyTorch model loaded successfully")
    return model

def load_onnx_model(onnx_path):
    """Load ONNX model"""
    try:
        import onnxruntime as ort
        
        print(f"🔄 Loading ONNX model: {onnx_path}")
        
        # Use CPU provider for consistent results
        providers = ['CPUExecutionProvider']
        session = ort.InferenceSession(onnx_path, providers=providers)
        
        print("✅ ONNX model loaded successfully")
        return session
        
    except ImportError:
        print("❌ ONNX Runtime not installed. Install with: pip install onnxruntime")
        return None
    except Exception as e:
        print(f"❌ Failed to load ONNX model: {e}")
        return None

def test_model_accuracy(pytorch_model, onnx_session, device, num_tests=20):
    """Test accuracy between PyTorch and ONNX models"""
    print(f"🧪 Testing model accuracy with {num_tests} samples...")
    
    differences = []
    pytorch_times = []
    onnx_times = []
    
    for i in range(num_tests):
        # Create test input
        test_input = torch.randn(1, 3, 256, 256, dtype=torch.float32)
        
        # PyTorch inference
        with torch.no_grad():
            start_time = time.perf_counter()
            pytorch_output = pytorch_model(test_input.to(device))
            pytorch_time = (time.perf_counter() - start_time) * 1000
            pytorch_times.append(pytorch_time)
            pytorch_np = pytorch_output.cpu().numpy()
        
        # ONNX inference
        ort_inputs = {onnx_session.get_inputs()[0].name: test_input.numpy()}
        start_time = time.perf_counter()
        onnx_outputs = onnx_session.run(None, ort_inputs)
        onnx_time = (time.perf_counter() - start_time) * 1000
        onnx_times.append(onnx_time)
        onnx_np = onnx_outputs[0]
        
        # Calculate differences
        abs_diff = np.abs(pytorch_np - onnx_np)
        max_diff = abs_diff.max()
        mean_diff = abs_diff.mean()
        
        # Calculate relative differences
        pytorch_abs = np.abs(pytorch_np)
        rel_diff = abs_diff / (pytorch_abs + 1e-8)
        max_rel_diff = rel_diff.max()
        
        differences.append({
            'max_abs': max_diff,
            'mean_abs': mean_diff,
            'max_rel': max_rel_diff
        })
    
    # Calculate statistics
    avg_pytorch_time = np.mean(pytorch_times)
    avg_onnx_time = np.mean(onnx_times)
    speedup = avg_pytorch_time / avg_onnx_time
    
    max_abs_diff = max(d['max_abs'] for d in differences)
    mean_abs_diff = np.mean([d['mean_abs'] for d in differences])
    max_rel_diff = max(d['max_rel'] for d in differences)
    
    return {
        'pytorch_time': avg_pytorch_time,
        'onnx_time': avg_onnx_time,
        'speedup': speedup,
        'max_abs_diff': max_abs_diff,
        'mean_abs_diff': mean_abs_diff,
        'max_rel_diff': max_rel_diff
    }

def test_real_image(pytorch_model, onnx_session, image_path, device):
    """Test with a real chess board image"""
    print(f"🖼️ Testing with real image: {image_path}")
    
    if not os.path.exists(image_path):
        print(f"⚠️ Image not found: {image_path}")
        return None
    
    # Load and preprocess image
    image = cv2.imread(image_path)
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    image_resized = cv2.resize(image_rgb, (256, 256))
    image_normalized = image_resized.astype(np.float32) / 255.0
    image_tensor = torch.from_numpy(image_normalized).permute(2, 0, 1).unsqueeze(0)
    
    # PyTorch inference
    with torch.no_grad():
        pytorch_output = pytorch_model(image_tensor.to(device))
        pytorch_mask = torch.sigmoid(pytorch_output).cpu().numpy()
    
    # ONNX inference
    ort_inputs = {onnx_session.get_inputs()[0].name: image_tensor.numpy()}
    onnx_outputs = onnx_session.run(None, ort_inputs)
    onnx_raw = onnx_outputs[0]
    onnx_mask = 1.0 / (1.0 + np.exp(-onnx_raw))  # Manual sigmoid
    
    # Calculate differences
    abs_diff = np.abs(pytorch_mask - onnx_mask)
    max_diff = abs_diff.max()
    mean_diff = abs_diff.mean()
    
    print(f"📊 Real Image Test Results:")
    print(f"   Max Difference: {max_diff:.6f}")
    print(f"   Mean Difference: {mean_diff:.6f}")
    
    return {
        'max_diff': max_diff,
        'mean_diff': mean_diff,
        'pytorch_mask': pytorch_mask,
        'onnx_mask': onnx_mask
    }

def print_accuracy_assessment(results):
    """Print detailed accuracy assessment"""
    print("\n" + "="*70)
    print("🏆 ONNX MODEL ACCURACY VALIDATION RESULTS")
    print("="*70)
    
    print("⚡ PERFORMANCE:")
    print(f"   PyTorch Time: {results['pytorch_time']:.2f}ms")
    print(f"   ONNX Time: {results['onnx_time']:.2f}ms")
    print(f"   Speedup: {results['speedup']:.2f}x")
    
    print("\n🎯 ACCURACY METRICS:")
    print(f"   Max Absolute Difference: {results['max_abs_diff']:.8f}")
    print(f"   Mean Absolute Difference: {results['mean_abs_diff']:.8f}")
    print(f"   Max Relative Difference: {results['max_rel_diff']:.6f}")
    
    # Accuracy classification
    max_diff = results['max_abs_diff']
    if max_diff < 1e-6:
        accuracy_level = "🏆 EXCELLENT (Near-perfect match)"
        recommendation = "✅ Ready for production deployment"
    elif max_diff < 1e-5:
        accuracy_level = "✅ VERY GOOD (Excellent for production)"
        recommendation = "✅ Recommended for production use"
    elif max_diff < 1e-4:
        accuracy_level = "✅ GOOD (Suitable for most applications)"
        recommendation = "✅ Acceptable for production use"
    elif max_diff < 1e-3:
        accuracy_level = "⚠️ MODERATE (May need validation)"
        recommendation = "⚠️ Test thoroughly before production"
    else:
        accuracy_level = "❌ POOR (Significant differences)"
        recommendation = "❌ Not recommended for production"
    
    print(f"\n📋 ACCURACY ASSESSMENT: {accuracy_level}")
    print(f"💡 RECOMMENDATION: {recommendation}")
    
    print("="*70)

def main():
    """Main validation function"""
    # Configuration
    pytorch_model_path = "ultimate_v2_breakthrough/ultimate_v2_breakthrough.pth"
    onnx_model_path = "ultimate_v2_breakthrough/ultimate_v2_breakthrough.onnx"
    test_image_path = r"C:\Users\<USER>\OneDrive\Desktop\New folder (4)\24.jpg"
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Using device: {device}")
    
    try:
        # Load models
        pytorch_model = load_pytorch_model(pytorch_model_path, device)
        onnx_session = load_onnx_model(onnx_model_path)
        
        if onnx_session is None:
            return
        
        # Test accuracy with random inputs
        results = test_model_accuracy(pytorch_model, onnx_session, device)
        
        # Test with real image
        if os.path.exists(test_image_path):
            real_results = test_real_image(pytorch_model, onnx_session, test_image_path, device)
            if real_results:
                print(f"\n🖼️ Real Image Accuracy:")
                print(f"   Max Difference: {real_results['max_diff']:.6f}")
                print(f"   Mean Difference: {real_results['mean_diff']:.6f}")
        
        # Print assessment
        print_accuracy_assessment(results)
        
        # File info
        pytorch_size = os.path.getsize(pytorch_model_path) / (1024 * 1024)
        onnx_size = os.path.getsize(onnx_model_path) / (1024 * 1024)
        
        print(f"\n📁 FILE SIZES:")
        print(f"   PyTorch Model: {pytorch_size:.2f}MB")
        print(f"   ONNX Model: {onnx_size:.2f}MB")
        print(f"   Size Difference: {abs(pytorch_size - onnx_size):.2f}MB")
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
