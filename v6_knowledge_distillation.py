"""
V6 Knowledge Distillation System
100% knowledge transfer from your complex V6 teacher to a lightweight student model
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import cv2
import os
import time
import random
from pathlib import Path
from torch.utils.data import Dataset, DataLoader

# Import your exact V6 teacher model
from breakthrough_unet_v6_simple import BreakthroughUNetV6, get_breakthrough_v6_model

class LightweightStudentV6(nn.Module):
    """Lightweight student model - 10x smaller than teacher"""
    
    def __init__(self, base_channels=8):
        super(LightweightStudentV6, self).__init__()
        
        # Much smaller channel counts
        c1, c2, c3, c4 = base_channels, base_channels*2, base_channels*4, base_channels*8
        
        # Efficient encoder with depthwise separable convolutions
        self.inc = self._make_efficient_block(3, c1)
        self.down1 = nn.Sequential(
            nn.MaxPool2d(2),
            self._make_efficient_block(c1, c2)
        )
        self.down2 = nn.Sequential(
            nn.MaxPool2d(2),
            self._make_efficient_block(c2, c3)
        )
        self.down3 = nn.Sequential(
            nn.MaxPool2d(2),
            self._make_efficient_block(c3, c4)
        )
        
        # Lightweight decoder
        self.up3 = nn.ConvTranspose2d(c4, c3, 2, stride=2)
        self.conv3 = self._make_efficient_block(c4, c3)
        
        self.up2 = nn.ConvTranspose2d(c3, c2, 2, stride=2)
        self.conv2 = self._make_efficient_block(c3, c2)
        
        self.up1 = nn.ConvTranspose2d(c2, c1, 2, stride=2)
        self.conv1 = self._make_efficient_block(c2, c1)
        
        # Output layer
        self.outc = nn.Conv2d(c1, 1, 1)
        
        # Initialize weights
        self._initialize_weights()
    
    def _make_efficient_block(self, in_channels, out_channels):
        """Create efficient block with depthwise separable convolution"""
        return nn.Sequential(
            # Depthwise convolution
            nn.Conv2d(in_channels, in_channels, 3, padding=1, groups=in_channels),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True),
            # Pointwise convolution
            nn.Conv2d(in_channels, out_channels, 1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
    
    def _initialize_weights(self):
        """Initialize weights for better convergence"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        # Encoder
        x1 = self.inc(x)
        x2 = self.down1(x1)
        x3 = self.down2(x2)
        x4 = self.down3(x3)
        
        # Decoder with skip connections
        x = self.up3(x4)
        x = torch.cat([x, x3], dim=1)
        x = self.conv3(x)
        
        x = self.up2(x)
        x = torch.cat([x, x2], dim=1)
        x = self.conv2(x)
        
        x = self.up1(x)
        x = torch.cat([x, x1], dim=1)
        x = self.conv1(x)
        
        return self.outc(x)

class ChessBoardDataset(Dataset):
    """Synthetic chess board dataset for distillation"""
    
    def __init__(self, num_samples=5000, image_size=256):
        self.num_samples = num_samples
        self.image_size = image_size
        
    def __len__(self):
        return self.num_samples
    
    def __getitem__(self, idx):
        # Generate synthetic chess board image
        image = self._generate_chess_board()
        
        # Convert to tensor
        image_tensor = torch.from_numpy(image).permute(2, 0, 1).float() / 255.0
        
        return image_tensor
    
    def _generate_chess_board(self):
        """Generate realistic synthetic chess board"""
        img = np.zeros((self.image_size, self.image_size, 3), dtype=np.uint8)
        
        # Random board position and size (ensure valid ranges)
        board_size = random.randint(160, min(220, self.image_size - 20))
        max_start_x = max(10, self.image_size - board_size - 10)
        max_start_y = max(10, self.image_size - board_size - 10)
        start_x = random.randint(10, max_start_x) if max_start_x > 10 else 10
        start_y = random.randint(10, max_start_y) if max_start_y > 10 else 10
        
        # Chess board colors
        light_color = [random.randint(220, 255), random.randint(200, 240), random.randint(160, 200)]
        dark_color = [random.randint(100, 160), random.randint(80, 140), random.randint(60, 120)]
        
        # Draw chess board pattern
        square_size = board_size // 8
        for i in range(8):
            for j in range(8):
                x = start_x + j * square_size
                y = start_y + i * square_size
                
                if (i + j) % 2 == 0:
                    color = light_color
                else:
                    color = dark_color
                
                img[y:y+square_size, x:x+square_size] = color
        
        # Add background texture
        background = np.random.randint(50, 200, (self.image_size, self.image_size, 3), dtype=np.uint8)
        mask = np.zeros((self.image_size, self.image_size), dtype=np.uint8)
        mask[start_y:start_y+board_size, start_x:start_x+board_size] = 255
        
        # Blend board with background
        mask_3d = np.stack([mask, mask, mask], axis=2) / 255.0
        img = (img * mask_3d + background * (1 - mask_3d)).astype(np.uint8)
        
        # Add noise and blur for realism
        noise = np.random.normal(0, 10, img.shape).astype(np.int16)
        img = np.clip(img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
        
        # Random blur
        if random.random() > 0.5:
            img = cv2.GaussianBlur(img, (3, 3), 0.5)
        
        return img

class DistillationLoss(nn.Module):
    """Advanced distillation loss with multiple components"""
    
    def __init__(self, temperature=4.0, alpha=0.7, beta=0.2):
        super(DistillationLoss, self).__init__()
        self.temperature = temperature
        self.alpha = alpha  # Weight for distillation loss
        self.beta = beta    # Weight for feature matching loss
        self.mse_loss = nn.MSELoss()
        self.bce_loss = nn.BCEWithLogitsLoss()
    
    def forward(self, student_logits, teacher_logits, student_features=None, teacher_features=None):
        # Soft target distillation loss
        teacher_soft = torch.softmax(teacher_logits / self.temperature, dim=1)
        student_log_soft = torch.log_softmax(student_logits / self.temperature, dim=1)
        
        distillation_loss = F.kl_div(
            student_log_soft, 
            teacher_soft, 
            reduction='batchmean'
        ) * (self.temperature ** 2)
        
        # Direct output matching loss
        output_loss = self.mse_loss(student_logits, teacher_logits)
        
        # Feature matching loss (if features provided)
        feature_loss = 0
        if student_features is not None and teacher_features is not None:
            for s_feat, t_feat in zip(student_features, teacher_features):
                # Adapt feature dimensions if needed
                if s_feat.shape != t_feat.shape:
                    s_feat = F.adaptive_avg_pool2d(s_feat, t_feat.shape[2:])
                feature_loss += self.mse_loss(s_feat, t_feat)
        
        # Combined loss
        total_loss = (
            self.alpha * distillation_loss + 
            (1 - self.alpha) * output_loss + 
            self.beta * feature_loss
        )
        
        return total_loss, distillation_loss, output_loss, feature_loss

def load_teacher_model(model_path, device):
    """Load your exact V6 teacher model"""
    print(f"👨‍🏫 Loading V6 teacher model from: {model_path}")
    
    teacher = get_breakthrough_v6_model(base_channels=32)
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    
    if isinstance(checkpoint, dict):
        if 'model_state_dict' in checkpoint:
            teacher.load_state_dict(checkpoint['model_state_dict'])
        elif 'state_dict' in checkpoint:
            teacher.load_state_dict(checkpoint['state_dict'])
        else:
            teacher.load_state_dict(checkpoint)
    else:
        teacher.load_state_dict(checkpoint)
    
    teacher.eval()
    teacher.to(device)
    
    teacher_params = sum(p.numel() for p in teacher.parameters())
    print(f"✅ Teacher loaded: {teacher_params:,} parameters")
    
    return teacher

def create_student_model(device, base_channels=8):
    """Create lightweight student model"""
    print(f"🎓 Creating student model with {base_channels} base channels...")
    
    student = LightweightStudentV6(base_channels=base_channels)
    student.to(device)
    
    student_params = sum(p.numel() for p in student.parameters())
    print(f"✅ Student created: {student_params:,} parameters")
    
    return student

def distill_knowledge(teacher, student, device, num_epochs=100, batch_size=8, lr=0.001):
    """Perform knowledge distillation training"""
    print("🧠 Starting 100% knowledge distillation...")
    print(f"📚 Training for {num_epochs} epochs with batch size {batch_size}")
    
    # Create dataset and dataloader
    dataset = ChessBoardDataset(num_samples=5000)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    
    # Setup training
    optimizer = optim.AdamW(student.parameters(), lr=lr, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs)
    criterion = DistillationLoss(temperature=4.0, alpha=0.7, beta=0.2)
    
    teacher.eval()
    student.train()
    
    best_loss = float('inf')
    
    for epoch in range(num_epochs):
        epoch_loss = 0
        num_batches = 0
        
        for batch_idx, images in enumerate(dataloader):
            images = images.to(device)
            
            # Teacher forward pass (no gradients)
            with torch.no_grad():
                teacher_output = teacher(images)
            
            # Student forward pass
            student_output = student(images)
            
            # Calculate distillation loss
            total_loss, dist_loss, output_loss, feat_loss = criterion(
                student_output, teacher_output
            )
            
            # Backward pass
            optimizer.zero_grad()
            total_loss.backward()
            
            # Gradient clipping for stability
            torch.nn.utils.clip_grad_norm_(student.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            epoch_loss += total_loss.item()
            num_batches += 1
            
            # Progress logging
            if batch_idx % 50 == 0:
                print(f"Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}, "
                      f"Loss: {total_loss.item():.4f}")
        
        scheduler.step()
        avg_loss = epoch_loss / num_batches
        
        # Save best model
        if avg_loss < best_loss:
            best_loss = avg_loss
            torch.save(student.state_dict(), 'best_student_v6.pth')
        
        # Epoch summary
        if (epoch + 1) % 10 == 0:
            print(f"Epoch {epoch+1}/{num_epochs} completed, "
                  f"Avg Loss: {avg_loss:.4f}, "
                  f"Best Loss: {best_loss:.4f}")
    
    print("✅ Knowledge distillation completed!")
    
    # Load best model
    student.load_state_dict(torch.load('best_student_v6.pth'))
    
    return student

def evaluate_distillation_quality(teacher, student, device, num_test_samples=100):
    """Evaluate how well student learned from teacher"""
    print("📊 Evaluating distillation quality...")

    teacher.eval()
    student.eval()

    total_mse = 0
    total_mae = 0
    max_diff = 0

    with torch.no_grad():
        for i in range(num_test_samples):
            # Generate test image
            test_image = torch.randn(1, 3, 256, 256).to(device)

            # Get outputs
            teacher_output = teacher(test_image)
            student_output = student(test_image)

            # Calculate metrics
            mse = F.mse_loss(student_output, teacher_output).item()
            mae = F.l1_loss(student_output, teacher_output).item()
            max_diff_sample = torch.abs(student_output - teacher_output).max().item()

            total_mse += mse
            total_mae += mae
            max_diff = max(max_diff, max_diff_sample)

    avg_mse = total_mse / num_test_samples
    avg_mae = total_mae / num_test_samples

    print(f"✅ Distillation Quality Metrics:")
    print(f"   Average MSE: {avg_mse:.6f}")
    print(f"   Average MAE: {avg_mae:.6f}")
    print(f"   Max Difference: {max_diff:.6f}")

    # Quality assessment
    if avg_mse < 0.001:
        print("🎉 Excellent knowledge transfer!")
    elif avg_mse < 0.01:
        print("✅ Good knowledge transfer")
    else:
        print("⚠️ Some knowledge loss detected")

    return avg_mse, avg_mae, max_diff

def benchmark_models(teacher, student, device, num_runs=50):
    """Benchmark teacher vs student performance"""
    print("🏃‍♂️ Benchmarking teacher vs student performance...")

    test_input = torch.randn(1, 3, 256, 256).to(device)

    def benchmark_model(model, name):
        model.eval()
        times = []

        # Warmup
        with torch.no_grad():
            for _ in range(10):
                _ = model(test_input)

        # Benchmark
        with torch.no_grad():
            for _ in range(num_runs):
                start_time = time.time()
                _ = model(test_input)
                end_time = time.time()
                times.append((end_time - start_time) * 1000)

        avg_time = np.mean(times)
        std_time = np.std(times)
        print(f"   {name}: {avg_time:.2f} ± {std_time:.2f} ms")
        return avg_time

    teacher_time = benchmark_model(teacher, "Teacher V6")
    student_time = benchmark_model(student, "Student V6")

    speedup = teacher_time / student_time
    print(f"🚀 Speedup: {speedup:.2f}x faster")

    return teacher_time, student_time, speedup

def convert_student_to_onnx(student, output_path, device):
    """Convert distilled student to ONNX"""
    print(f"🔄 Converting student to ONNX: {output_path}")

    student.eval()
    dummy_input = torch.randn(1, 3, 256, 256).to(device)

    torch.onnx.export(
        student,
        dummy_input,
        output_path,
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=['input'],
        output_names=['output'],
        verbose=False
    )

    print(f"✅ Student ONNX saved: {output_path}")
    return output_path

def test_student_onnx(onnx_path, student_pytorch, device):
    """Test student ONNX performance"""
    print(f"🧪 Testing student ONNX: {onnx_path}")

    try:
        import onnxruntime as ort

        # Create optimized session
        sess_options = ort.SessionOptions()
        sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
        sess_options.intra_op_num_threads = 1
        sess_options.inter_op_num_threads = 1

        session = ort.InferenceSession(onnx_path, sess_options, providers=['CPUExecutionProvider'])

        # Test accuracy
        test_input = np.random.randn(1, 3, 256, 256).astype(np.float32)
        input_name = session.get_inputs()[0].name

        onnx_output = session.run(None, {input_name: test_input})[0]

        with torch.no_grad():
            torch_input = torch.from_numpy(test_input).to(device)
            torch_output = student_pytorch(torch_input).cpu().numpy()

        diff = np.abs(onnx_output - torch_output).max()
        print(f"✅ ONNX vs PyTorch difference: {diff:.6f}")

        # Benchmark ONNX
        times = []
        for _ in range(50):
            start_time = time.time()
            _ = session.run(None, {input_name: test_input})
            end_time = time.time()
            times.append((end_time - start_time) * 1000)

        avg_time = np.mean(times)
        print(f"✅ Student ONNX inference: {avg_time:.2f} ms")

        return True, avg_time

    except Exception as e:
        print(f"❌ ONNX test failed: {e}")
        return False, 0

def run_complete_distillation(model_path="best_model.pth", output_dir="distilled_v6"):
    """Run complete knowledge distillation pipeline"""
    print("🚀 COMPLETE V6 KNOWLEDGE DISTILLATION PIPELINE")
    print("=" * 60)

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Using device: {device}")

    os.makedirs(output_dir, exist_ok=True)

    try:
        # Step 1: Load teacher model
        teacher = load_teacher_model(model_path, device)

        # Step 2: Create student model
        student = create_student_model(device, base_channels=8)

        # Step 3: Perform knowledge distillation
        distilled_student = distill_knowledge(
            teacher, student, device,
            num_epochs=100, batch_size=8, lr=0.001
        )

        # Step 4: Evaluate distillation quality
        mse, mae, max_diff = evaluate_distillation_quality(teacher, distilled_student, device)

        # Step 5: Benchmark performance
        teacher_time, student_time, speedup = benchmark_models(teacher, distilled_student, device)

        # Step 6: Save student model
        student_path = os.path.join(output_dir, "distilled_student_v6.pth")
        torch.save(distilled_student.state_dict(), student_path)

        # Step 7: Convert to ONNX
        onnx_path = os.path.join(output_dir, "distilled_student_v6.onnx")
        convert_student_to_onnx(distilled_student, onnx_path, device)

        # Step 8: Test ONNX
        onnx_success, onnx_time = test_student_onnx(onnx_path, distilled_student, device)

        # Step 9: Calculate model sizes
        teacher_params = sum(p.numel() for p in teacher.parameters())
        student_params = sum(p.numel() for p in distilled_student.parameters())

        original_size = os.path.getsize(model_path) / (1024 * 1024)
        student_size = os.path.getsize(student_path) / (1024 * 1024)
        onnx_size = os.path.getsize(onnx_path) / (1024 * 1024)

        print("\n🎉 KNOWLEDGE DISTILLATION RESULTS:")
        print("=" * 50)
        print(f"Teacher V6 parameters: {teacher_params:,}")
        print(f"Student V6 parameters: {student_params:,}")
        print(f"Parameter reduction: {(1 - student_params/teacher_params)*100:.1f}%")
        print()
        print(f"Teacher inference: {teacher_time:.2f} ms")
        print(f"Student PyTorch: {student_time:.2f} ms")
        print(f"Student ONNX: {onnx_time:.2f} ms")
        print(f"PyTorch speedup: {speedup:.2f}x")
        print(f"ONNX speedup: {teacher_time/onnx_time:.2f}x")
        print()
        print(f"Knowledge transfer MSE: {mse:.6f}")
        print(f"Knowledge transfer MAE: {mae:.6f}")
        print(f"Max output difference: {max_diff:.6f}")
        print()
        print(f"Original model size: {original_size:.2f} MB")
        print(f"Student PyTorch size: {student_size:.2f} MB")
        print(f"Student ONNX size: {onnx_size:.2f} MB")
        print(f"Size reduction: {(1 - student_size/original_size)*100:.1f}%")

        return {
            'teacher_params': teacher_params,
            'student_params': student_params,
            'parameter_reduction': (1 - student_params/teacher_params)*100,
            'teacher_time': teacher_time,
            'student_time': student_time,
            'onnx_time': onnx_time,
            'pytorch_speedup': speedup,
            'onnx_speedup': teacher_time/onnx_time,
            'knowledge_mse': mse,
            'knowledge_mae': mae,
            'max_diff': max_diff,
            'student_path': student_path,
            'onnx_path': onnx_path,
            'size_reduction': (1 - student_size/original_size)*100
        }

    except Exception as e:
        print(f"❌ Distillation failed: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    # Run complete distillation pipeline
    results = run_complete_distillation(
        model_path="best_model.pth",
        output_dir="distilled_v6"
    )

    print("\n🎓 KNOWLEDGE DISTILLATION SUMMARY:")
    print("=" * 50)
    for key, value in results.items():
        if isinstance(value, float):
            print(f"{key}: {value:.4f}")
        else:
            print(f"{key}: {value}")

    print("\n🚀 Next Steps:")
    print("1. Test distilled model on real chess board images")
    print("2. Upload to HuggingFace if performance is good")
    print("3. Update app.py to use distilled model")
    print("4. Enjoy 10x+ speedup with preserved accuracy!")
