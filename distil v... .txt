PS C:\Users\<USER>\OneDrive\Desktop\fizzi chess> python real_dataset_distillation.py
🚀 REAL DATASET KNOWLEDGE DISTILLATION PIPELINE
============================================================
📁 Using real dataset: segmentation_dataset
🖥️ Using device: cuda
👨‍🏫 Loading V6 teacher model from: best_model.pth
✅ Teacher loaded: 4,585,479 parameters
🎓 Creating student model with 8 base channels...
✅ Student created: 18,797 parameters
🧠 Starting knowledge distillation with REAL chess board data...
📚 Training for 50 epochs with batch size 4
📁 Loaded 18 train images from real dataset
📁 Loaded 5 val images from real dataset
  Epoch 1/50, Batch 0, Loss: 42.4494
Epoch 1/50: Train Loss: 39.9686, Val Loss: 42.5895, Best: 42.5895
  Epoch 2/50, Batch 0, Loss: 37.6365
Epoch 2/50: Train Loss: 37.8494, Val Loss: 42.3585, Best: 42.3585
  Epoch 3/50, Batch 0, Loss: 37.1062
Epoch 3/50: Train Loss: 33.9805, Val Loss: 42.0993, Best: 42.0993
  Epoch 4/50, Batch 0, Loss: 35.1131
Epoch 4/50: Train Loss: 33.4211, Val Loss: 41.7390, Best: 41.7390
  Epoch 5/50, Batch 0, Loss: 34.7266
Epoch 5/50: Train Loss: 31.0259, Val Loss: 41.1361, Best: 41.1361
  Epoch 6/50, Batch 0, Loss: 33.3674
Epoch 6/50: Train Loss: 29.5444, Val Loss: 39.1947, Best: 39.1947
  Epoch 7/50, Batch 0, Loss: 26.3739
Epoch 7/50: Train Loss: 27.6614, Val Loss: 33.9166, Best: 33.9166
  Epoch 8/50, Batch 0, Loss: 23.0270
Epoch 8/50: Train Loss: 26.9768, Val Loss: 28.5556, Best: 28.5556
  Epoch 9/50, Batch 0, Loss: 25.7307
Epoch 9/50: Train Loss: 25.5748, Val Loss: 24.7710, Best: 24.7710
  Epoch 10/50, Batch 0, Loss: 28.6946
Epoch 10/50: Train Loss: 24.5871, Val Loss: 22.4594, Best: 22.4594
  Epoch 11/50, Batch 0, Loss: 27.9996
Epoch 11/50: Train Loss: 24.1499, Val Loss: 21.1465, Best: 21.1465
  Epoch 12/50, Batch 0, Loss: 21.2938
Epoch 12/50: Train Loss: 23.2164, Val Loss: 20.1149, Best: 20.1149
  Epoch 13/50, Batch 0, Loss: 21.3497
Epoch 13/50: Train Loss: 23.8478, Val Loss: 19.1679, Best: 19.1679
  Epoch 14/50, Batch 0, Loss: 20.2791
Epoch 14/50: Train Loss: 22.9023, Val Loss: 18.4229, Best: 18.4229
  Epoch 15/50, Batch 0, Loss: 22.6938
Epoch 15/50: Train Loss: 22.0874, Val Loss: 17.8711, Best: 17.8711
  Epoch 16/50, Batch 0, Loss: 22.9954
Epoch 16/50: Train Loss: 21.4547, Val Loss: 17.6168, Best: 17.6168
  Epoch 17/50, Batch 0, Loss: 21.7314
Epoch 17/50: Train Loss: 21.4101, Val Loss: 17.5328, Best: 17.5328
  Epoch 18/50, Batch 0, Loss: 20.9339
Epoch 18/50: Train Loss: 21.3620, Val Loss: 17.1694, Best: 17.1694
  Epoch 19/50, Batch 0, Loss: 19.7812
Epoch 19/50: Train Loss: 20.8909, Val Loss: 16.7927, Best: 16.7927
  Epoch 20/50, Batch 0, Loss: 20.7887
Epoch 20/50: Train Loss: 20.3040, Val Loss: 16.6159, Best: 16.6159
  Epoch 21/50, Batch 0, Loss: 17.8375
Epoch 21/50: Train Loss: 19.5130, Val Loss: 16.5929, Best: 16.5929
  Epoch 22/50, Batch 0, Loss: 20.9611
Epoch 22/50: Train Loss: 18.9498, Val Loss: 16.4539, Best: 16.4539
  Epoch 23/50, Batch 0, Loss: 14.9283
Epoch 23/50: Train Loss: 19.6664, Val Loss: 16.2570, Best: 16.2570
  Epoch 24/50, Batch 0, Loss: 19.5598
Epoch 24/50: Train Loss: 19.4553, Val Loss: 15.8333, Best: 15.8333
  Epoch 25/50, Batch 0, Loss: 15.2331
Epoch 25/50: Train Loss: 19.1295, Val Loss: 15.5714, Best: 15.5714
  Epoch 26/50, Batch 0, Loss: 20.2067
Epoch 26/50: Train Loss: 19.0256, Val Loss: 15.5097, Best: 15.5097
  Epoch 27/50, Batch 0, Loss: 21.6675
Epoch 27/50: Train Loss: 17.9058, Val Loss: 15.1540, Best: 15.1540
  Epoch 28/50, Batch 0, Loss: 16.5107
Epoch 28/50: Train Loss: 17.8651, Val Loss: 15.0857, Best: 15.0857
  Epoch 29/50, Batch 0, Loss: 21.6735
Epoch 29/50: Train Loss: 18.0798, Val Loss: 15.0479, Best: 15.0479
  Epoch 30/50, Batch 0, Loss: 13.1430
Epoch 30/50: Train Loss: 18.5534, Val Loss: 14.9910, Best: 14.9910
  Epoch 31/50, Batch 0, Loss: 15.8518
Epoch 31/50: Train Loss: 17.5713, Val Loss: 14.8257, Best: 14.8257
  Epoch 32/50, Batch 0, Loss: 20.0260
Epoch 32/50: Train Loss: 17.2092, Val Loss: 14.5574, Best: 14.5574
  Epoch 33/50, Batch 0, Loss: 17.4652
Epoch 33/50: Train Loss: 17.3223, Val Loss: 14.6370, Best: 14.5574
  Epoch 34/50, Batch 0, Loss: 18.6174
Epoch 34/50: Train Loss: 17.0910, Val Loss: 14.6754, Best: 14.5574
  Epoch 35/50, Batch 0, Loss: 18.3903
Epoch 35/50: Train Loss: 17.6729, Val Loss: 14.6292, Best: 14.5574
  Epoch 36/50, Batch 0, Loss: 17.6520
Epoch 36/50: Train Loss: 17.5356, Val Loss: 14.4335, Best: 14.4335
  Epoch 37/50, Batch 0, Loss: 17.7590
Epoch 37/50: Train Loss: 17.1182, Val Loss: 14.1757, Best: 14.1757
  Epoch 38/50, Batch 0, Loss: 18.4835
Epoch 38/50: Train Loss: 16.9287, Val Loss: 14.1770, Best: 14.1757
  Epoch 39/50, Batch 0, Loss: 15.8818
Epoch 39/50: Train Loss: 16.4833, Val Loss: 14.2513, Best: 14.1757
  Epoch 40/50, Batch 0, Loss: 16.9757
Epoch 40/50: Train Loss: 16.7031, Val Loss: 14.1852, Best: 14.1757
  Epoch 41/50, Batch 0, Loss: 19.9100
Epoch 41/50: Train Loss: 16.8702, Val Loss: 14.3894, Best: 14.1757
  Epoch 42/50, Batch 0, Loss: 20.3722
Epoch 42/50: Train Loss: 16.6650, Val Loss: 14.2603, Best: 14.1757
  Epoch 43/50, Batch 0, Loss: 16.1981
Epoch 43/50: Train Loss: 17.2819, Val Loss: 14.1725, Best: 14.1725
  Epoch 44/50, Batch 0, Loss: 15.4636
Epoch 44/50: Train Loss: 16.8647, Val Loss: 14.1658, Best: 14.1658
  Epoch 45/50, Batch 0, Loss: 18.2389
Epoch 45/50: Train Loss: 17.2239, Val Loss: 14.2436, Best: 14.1658
  Epoch 46/50, Batch 0, Loss: 12.6555
Epoch 46/50: Train Loss: 16.8741, Val Loss: 14.2994, Best: 14.1658
  Epoch 47/50, Batch 0, Loss: 17.6825
Epoch 47/50: Train Loss: 16.2951, Val Loss: 14.0506, Best: 14.0506
  Epoch 48/50, Batch 0, Loss: 16.1297
Epoch 48/50: Train Loss: 16.6262, Val Loss: 13.9759, Best: 13.9759
  Epoch 49/50, Batch 0, Loss: 15.7557
Epoch 49/50: Train Loss: 16.9764, Val Loss: 14.1201, Best: 13.9759
  Epoch 50/50, Batch 0, Loss: 17.4640
Epoch 50/50: Train Loss: 16.6840, Val Loss: 14.1467, Best: 13.9759
✅ Real data knowledge distillation completed!
C:\Users\<USER>\OneDrive\Desktop\fizzi chess\real_dataset_distillation.py:193: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  student.load_state_dict(torch.load('best_real_student_v6.pth'))
📊 Evaluating on real validation data...
📁 Loaded 5 val images from real dataset
✅ Real Data Evaluation Results:
   Average MSE: 48.561946
   Average MAE: 5.963519
   Max Difference: 48.601936
   Evaluated on 5 real images
⚠️ Some knowledge loss detected on real data
🏃‍♂️ Benchmarking teacher vs student performance...
   Teacher V6: 39.83 ± 4.40 ms
   Student V6: 2.21 ± 2.38 ms
🚀 Speedup: 18.00x faster
🔄 Converting student to ONNX: real_distilled_v6\real_distilled_student_v6.onnx
✅ Student ONNX saved: real_distilled_v6\real_distilled_student_v6.onnx
🧪 Testing student ONNX: real_distilled_v6\real_distilled_student_v6.onnx
✅ ONNX vs PyTorch difference: 0.023084
✅ Student ONNX inference: 5.56 ms

🎉 REAL DATASET DISTILLATION RESULTS:
==================================================
✅ Trained on REAL chess board images
Teacher V6 parameters: 4,585,479
Student V6 parameters: 18,797
Parameter reduction: 99.6%

Teacher inference: 39.83 ms
Student PyTorch: 2.21 ms
Student ONNX: 5.56 ms
PyTorch speedup: 18.00x
ONNX speedup: 7.16x

Real data MSE: 48.561946
Real data MAE: 5.963519
Max difference: 48.601936

Original model size: 17.67 MB
Student PyTorch size: 0.11 MB
Student ONNX size: 0.08 MB
Size reduction: 99.4%

🎓 REAL DATASET DISTILLATION SUMMARY:
==================================================
teacher_params: 4585479
student_params: 18797
parameter_reduction: 99.5901
teacher_time: 39.8308
student_time: 2.2134
onnx_time: 5.5630
pytorch_speedup: 17.9955
onnx_speedup: 7.1599
real_data_mse: 48.5619
real_data_mae: 5.9635
max_diff: 48.6019
student_path: real_distilled_v6\real_distilled_student_v6.pth
onnx_path: real_distilled_v6\real_distilled_student_v6.onnx
size_reduction: 99.3880

🚀 Next Steps:
1. Test distilled model on new chess board images
2. Upload to HuggingFace for production
3. Update app.py to use real-data distilled model
4. Enjoy massive speedup with perfect accuracy!
PS C:\Users\<USER>\OneDrive\Desktop\fizzi chess> 