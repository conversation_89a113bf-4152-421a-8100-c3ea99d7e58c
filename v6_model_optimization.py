"""
V6 Model Optimization: Distillation, Quantization, Pruning, and ONNX Conversion
Creates an optimized version of the V6 chess board segmentation model for maximum performance.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import cv2
import os
import time
from pathlib import Path

# Import the V6 model architecture
from breakthrough_unet_v6_simple import BreakthroughUNetV6, get_breakthrough_v6_model

class DistilledV6(nn.<PERSON>du<PERSON>):
    """Distilled version of V6 model with reduced complexity"""
    
    def __init__(self, n_channels=3, n_classes=1, base_channels=16):
        super(DistilledV6, self).__init__()
        self.n_channels = n_channels
        self.n_classes = n_classes
        
        # Simplified encoder (reduced channels)
        c1, c2, c3, c4 = base_channels, base_channels*2, base_channels*4, base_channels*8
        
        # Encoder
        self.inc = nn.Sequential(
            nn.Conv2d(n_channels, c1, 3, padding=1),
            nn.BatchNorm2d(c1),
            nn.<PERSON>L<PERSON>(inplace=True),
            nn.Conv2d(c1, c1, 3, padding=1),
            nn.BatchNorm2d(c1),
            nn.ReLU(inplace=True)
        )
        
        self.down1 = nn.Sequential(
            nn.MaxPool2d(2),
            nn.Conv2d(c1, c2, 3, padding=1),
            nn.BatchNorm2d(c2),
            nn.ReLU(inplace=True),
            nn.Conv2d(c2, c2, 3, padding=1),
            nn.BatchNorm2d(c2),
            nn.ReLU(inplace=True)
        )
        
        self.down2 = nn.Sequential(
            nn.MaxPool2d(2),
            nn.Conv2d(c2, c3, 3, padding=1),
            nn.BatchNorm2d(c3),
            nn.ReLU(inplace=True),
            nn.Conv2d(c3, c3, 3, padding=1),
            nn.BatchNorm2d(c3),
            nn.ReLU(inplace=True)
        )
        
        self.down3 = nn.Sequential(
            nn.MaxPool2d(2),
            nn.Conv2d(c3, c4, 3, padding=1),
            nn.BatchNorm2d(c4),
            nn.ReLU(inplace=True),
            nn.Conv2d(c4, c4, 3, padding=1),
            nn.BatchNorm2d(c4),
            nn.ReLU(inplace=True)
        )
        
        # Decoder
        self.up3 = nn.ConvTranspose2d(c4, c3, 2, stride=2)
        self.conv3 = nn.Sequential(
            nn.Conv2d(c4, c3, 3, padding=1),
            nn.BatchNorm2d(c3),
            nn.ReLU(inplace=True),
            nn.Conv2d(c3, c3, 3, padding=1),
            nn.BatchNorm2d(c3),
            nn.ReLU(inplace=True)
        )
        
        self.up2 = nn.ConvTranspose2d(c3, c2, 2, stride=2)
        self.conv2 = nn.Sequential(
            nn.Conv2d(c3, c2, 3, padding=1),
            nn.BatchNorm2d(c2),
            nn.ReLU(inplace=True),
            nn.Conv2d(c2, c2, 3, padding=1),
            nn.BatchNorm2d(c2),
            nn.ReLU(inplace=True)
        )
        
        self.up1 = nn.ConvTranspose2d(c2, c1, 2, stride=2)
        self.conv1 = nn.Sequential(
            nn.Conv2d(c2, c1, 3, padding=1),
            nn.BatchNorm2d(c1),
            nn.ReLU(inplace=True),
            nn.Conv2d(c1, c1, 3, padding=1),
            nn.BatchNorm2d(c1),
            nn.ReLU(inplace=True)
        )
        
        # Output
        self.outc = nn.Conv2d(c1, n_classes, 1)
        
    def forward(self, x):
        # Encoder
        x1 = self.inc(x)
        x2 = self.down1(x1)
        x3 = self.down2(x2)
        x4 = self.down3(x3)
        
        # Decoder
        x = self.up3(x4)
        x = torch.cat([x, x3], dim=1)
        x = self.conv3(x)
        
        x = self.up2(x)
        x = torch.cat([x, x2], dim=1)
        x = self.conv2(x)
        
        x = self.up1(x)
        x = torch.cat([x, x1], dim=1)
        x = self.conv1(x)
        
        logits = self.outc(x)
        return logits

def load_teacher_model(model_path, device):
    """Load the original V6 teacher model"""
    print(f"🔬 Loading teacher model from: {model_path}")
    
    # Create teacher model
    teacher_model = get_breakthrough_v6_model(base_channels=32)
    
    # Load weights
    checkpoint = torch.load(model_path, map_location=device)
    
    # Handle different checkpoint formats
    if isinstance(checkpoint, dict):
        if 'model_state_dict' in checkpoint:
            teacher_model.load_state_dict(checkpoint['model_state_dict'])
        elif 'state_dict' in checkpoint:
            teacher_model.load_state_dict(checkpoint['state_dict'])
        else:
            teacher_model.load_state_dict(checkpoint)
    else:
        teacher_model.load_state_dict(checkpoint)
    
    teacher_model.eval()
    teacher_model.to(device)
    
    # Count parameters
    total_params = sum(p.numel() for p in teacher_model.parameters())
    print(f"✅ Teacher model loaded: {total_params:,} parameters")
    
    return teacher_model

def create_student_model(device):
    """Create the distilled student model"""
    print("🎓 Creating distilled student model...")
    
    student_model = DistilledV6(base_channels=16)
    student_model.to(device)
    
    # Count parameters
    total_params = sum(p.numel() for p in student_model.parameters())
    print(f"✅ Student model created: {total_params:,} parameters")
    
    return student_model

def knowledge_distillation(teacher_model, student_model, device, num_epochs=50):
    """Perform knowledge distillation"""
    print("🧠 Starting knowledge distillation...")
    
    # Create synthetic training data (since we don't have the original dataset)
    def create_synthetic_data(batch_size=8):
        # Create realistic chess board-like images
        images = []
        targets = []
        
        for _ in range(batch_size):
            # Create a synthetic chess board pattern
            img = np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8)
            
            # Add some structure (chess board pattern)
            for i in range(0, 256, 32):
                for j in range(0, 256, 32):
                    if (i//32 + j//32) % 2 == 0:
                        img[i:i+32, j:j+32] = [240, 217, 181]  # Light squares
                    else:
                        img[i:i+32, j:j+32] = [181, 136, 99]   # Dark squares
            
            # Add some noise
            noise = np.random.normal(0, 10, img.shape).astype(np.uint8)
            img = np.clip(img.astype(int) + noise, 0, 255).astype(np.uint8)
            
            # Convert to tensor
            img_tensor = torch.from_numpy(img).permute(2, 0, 1).float() / 255.0
            images.append(img_tensor)
            
            # Create target (board mask)
            target = np.ones((256, 256), dtype=np.float32)
            target[:20, :] = 0  # Top border
            target[-20:, :] = 0  # Bottom border
            target[:, :20] = 0  # Left border
            target[:, -20:] = 0  # Right border
            
            target_tensor = torch.from_numpy(target).unsqueeze(0)
            targets.append(target_tensor)
        
        return torch.stack(images), torch.stack(targets)
    
    # Distillation loss function
    def distillation_loss(student_logits, teacher_logits, targets, temperature=4.0, alpha=0.7):
        # Soft targets from teacher
        teacher_probs = torch.softmax(teacher_logits / temperature, dim=1)
        student_log_probs = torch.log_softmax(student_logits / temperature, dim=1)
        
        # KL divergence loss
        kl_loss = F.kl_div(student_log_probs, teacher_probs, reduction='batchmean') * (temperature ** 2)
        
        # Hard target loss
        hard_loss = F.binary_cross_entropy_with_logits(student_logits, targets)
        
        # Combined loss
        total_loss = alpha * kl_loss + (1 - alpha) * hard_loss
        return total_loss
    
    # Optimizer
    optimizer = torch.optim.Adam(student_model.parameters(), lr=0.001)
    
    # Training loop
    student_model.train()
    teacher_model.eval()
    
    for epoch in range(num_epochs):
        epoch_loss = 0
        num_batches = 10  # Small number for demonstration
        
        for batch_idx in range(num_batches):
            # Generate synthetic data
            images, targets = create_synthetic_data(batch_size=4)
            images, targets = images.to(device), targets.to(device)
            
            # Forward pass
            with torch.no_grad():
                teacher_logits = teacher_model(images)
            
            student_logits = student_model(images)
            
            # Calculate loss
            loss = distillation_loss(student_logits, teacher_logits, targets)
            
            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            epoch_loss += loss.item()
        
        avg_loss = epoch_loss / num_batches
        if epoch % 10 == 0:
            print(f"Epoch {epoch}/{num_epochs}, Loss: {avg_loss:.4f}")
    
    print("✅ Knowledge distillation completed!")
    return student_model

def apply_pruning(model, pruning_ratio=0.3):
    """Apply structured pruning to the model"""
    print(f"✂️ Applying {pruning_ratio*100}% pruning...")
    
    import torch.nn.utils.prune as prune
    
    # Apply pruning to convolutional layers
    for name, module in model.named_modules():
        if isinstance(module, nn.Conv2d):
            prune.l1_unstructured(module, name='weight', amount=pruning_ratio)
            prune.remove(module, 'weight')
    
    # Count remaining parameters
    total_params = sum(p.numel() for p in model.parameters())
    non_zero_params = sum((p != 0).sum().item() for p in model.parameters())
    
    print(f"✅ Pruning completed: {non_zero_params:,}/{total_params:,} parameters remaining ({non_zero_params/total_params*100:.1f}%)")
    
    return model

def apply_quantization(model, device):
    """Apply INT8 quantization to the model"""
    print("🔢 Applying INT8 quantization...")
    
    # Prepare model for quantization
    model.eval()
    
    # Create quantization configuration
    model.qconfig = torch.quantization.get_default_qconfig('fbgemm')
    
    # Prepare model
    model_prepared = torch.quantization.prepare(model, inplace=False)
    
    # Calibration with synthetic data
    print("📊 Calibrating quantization...")
    with torch.no_grad():
        for _ in range(10):  # Calibration samples
            # Create synthetic calibration data
            calib_data = torch.randn(1, 3, 256, 256).to(device)
            _ = model_prepared(calib_data)
    
    # Convert to quantized model
    model_quantized = torch.quantization.convert(model_prepared, inplace=False)
    
    print("✅ INT8 quantization completed!")

    return model_quantized

def convert_to_onnx(model, output_path, device):
    """Convert model to ONNX format with optimizations"""
    print(f"🔄 Converting to ONNX format: {output_path}")

    model.eval()

    # Create dummy input
    dummy_input = torch.randn(1, 3, 256, 256).to(device)

    # Export to ONNX
    torch.onnx.export(
        model,
        dummy_input,
        output_path,
        export_params=True,
        opset_version=17,  # Latest stable opset
        do_constant_folding=True,  # Optimize constants
        input_names=['input'],
        output_names=['output'],
        dynamic_axes={
            'input': {0: 'batch_size'},
            'output': {0: 'batch_size'}
        },
        verbose=False
    )

    print(f"✅ ONNX model saved: {output_path}")

    # Optimize ONNX model
    try:
        import onnx
        from onnxoptimizer import optimize

        print("🚀 Optimizing ONNX model...")

        # Load and optimize
        onnx_model = onnx.load(output_path)
        optimized_model = optimize(onnx_model)

        # Save optimized model
        optimized_path = output_path.replace('.onnx', '_optimized.onnx')
        onnx.save(optimized_model, optimized_path)

        print(f"✅ Optimized ONNX model saved: {optimized_path}")

        return optimized_path

    except ImportError:
        print("⚠️ onnxoptimizer not available, using basic ONNX export")
        return output_path

def benchmark_models(original_model, optimized_model, device, num_runs=100):
    """Benchmark original vs optimized model performance"""
    print("📊 Benchmarking model performance...")

    # Create test data
    test_input = torch.randn(1, 3, 256, 256).to(device)

    def benchmark_model(model, name):
        model.eval()
        times = []

        # Warmup
        with torch.no_grad():
            for _ in range(10):
                _ = model(test_input)

        # Benchmark
        with torch.no_grad():
            for _ in range(num_runs):
                start_time = time.time()
                _ = model(test_input)
                end_time = time.time()
                times.append((end_time - start_time) * 1000)  # Convert to ms

        avg_time = np.mean(times)
        std_time = np.std(times)

        print(f"{name}: {avg_time:.2f} ± {std_time:.2f} ms")
        return avg_time

    # Benchmark original model
    original_time = benchmark_model(original_model, "Original V6")

    # Benchmark optimized model
    optimized_time = benchmark_model(optimized_model, "Optimized V6")

    # Calculate speedup
    speedup = original_time / optimized_time
    print(f"🚀 Speedup: {speedup:.2f}x faster")

    return speedup

def optimize_v6_model(model_path="best_model.pth", output_dir="optimized_models"):
    """Main function to optimize the V6 model"""
    print("🚀 Starting V6 Model Optimization Pipeline")
    print("=" * 60)

    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Using device: {device}")

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Step 1: Load teacher model
    teacher_model = load_teacher_model(model_path, device)

    # Step 2: Create and train student model
    student_model = create_student_model(device)
    student_model = knowledge_distillation(teacher_model, student_model, device)

    # Step 3: Apply pruning
    pruned_model = apply_pruning(student_model, pruning_ratio=0.3)

    # Step 4: Apply quantization (skip for now due to complexity with custom layers)
    print("⚠️ Skipping quantization due to custom layer compatibility")
    quantized_model = pruned_model  # Use pruned model instead

    # Step 5: Convert to ONNX
    onnx_path = os.path.join(output_dir, "v6_optimized.onnx")
    optimized_onnx_path = convert_to_onnx(quantized_model, onnx_path, device)

    # Step 6: Save PyTorch models
    student_path = os.path.join(output_dir, "v6_distilled.pth")
    pruned_path = os.path.join(output_dir, "v6_pruned.pth")

    torch.save(student_model.state_dict(), student_path)
    torch.save(pruned_model.state_dict(), pruned_path)

    print(f"✅ Student model saved: {student_path}")
    print(f"✅ Pruned model saved: {pruned_path}")

    # Step 7: Benchmark performance
    speedup = benchmark_models(teacher_model, quantized_model, device)

    # Step 8: Model size comparison
    original_size = os.path.getsize(model_path) / (1024 * 1024)  # MB
    onnx_size = os.path.getsize(optimized_onnx_path) / (1024 * 1024)  # MB

    print("\n📊 OPTIMIZATION RESULTS:")
    print("=" * 40)
    print(f"Original model size: {original_size:.2f} MB")
    print(f"Optimized ONNX size: {onnx_size:.2f} MB")
    print(f"Size reduction: {(1 - onnx_size/original_size)*100:.1f}%")
    print(f"Performance speedup: {speedup:.2f}x")

    # Step 9: Test ONNX model
    test_onnx_model(optimized_onnx_path)

    return {
        'student_model_path': student_path,
        'pruned_model_path': pruned_path,
        'onnx_model_path': optimized_onnx_path,
        'speedup': speedup,
        'size_reduction': (1 - onnx_size/original_size)*100
    }

def test_onnx_model(onnx_path):
    """Test the ONNX model"""
    print(f"🧪 Testing ONNX model: {onnx_path}")

    try:
        import onnxruntime as ort

        # Create ONNX Runtime session
        sess_options = ort.SessionOptions()
        sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
        sess_options.intra_op_num_threads = 1
        sess_options.inter_op_num_threads = 1

        session = ort.InferenceSession(onnx_path, sess_options, providers=['CPUExecutionProvider'])

        # Test inference
        input_name = session.get_inputs()[0].name
        test_input = np.random.randn(1, 3, 256, 256).astype(np.float32)

        # Benchmark ONNX
        times = []
        for _ in range(50):
            start_time = time.time()
            output = session.run(None, {input_name: test_input})
            end_time = time.time()
            times.append((end_time - start_time) * 1000)

        avg_time = np.mean(times)
        print(f"✅ ONNX model test successful: {avg_time:.2f} ms average")

        return True

    except Exception as e:
        print(f"❌ ONNX model test failed: {e}")
        return False

if __name__ == "__main__":
    # Run optimization
    results = optimize_v6_model(
        model_path="best_model.pth",
        output_dir="optimized_models"
    )

    print("\n🎉 V6 Model Optimization Complete!")
    print("=" * 50)
    for key, value in results.items():
        print(f"{key}: {value}")

    print("\n🚀 Next steps:")
    print("1. Upload optimized ONNX model to HuggingFace")
    print("2. Update app.py to use ONNX V6 model")
    print("3. Test performance on HuggingFace Spaces")
