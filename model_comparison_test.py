"""
Model Comparison Test: V6 Original vs Ultimate V2 Breakthrough (V3 Distilled)
Tests both models on a single image with detailed visualization and performance comparison.
"""

import torch
import torch.nn as nn
import cv2
import numpy as np
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import time
import os
from pathlib import Path

# Import model architectures
from breakthrough_unet_v6_simple import get_breakthrough_v6_model
from ultimate_breakthrough_v2 import UltimateBreakthroughV2

def load_v6_original(model_path, device):
    """Load the original V6 teacher model"""
    print("👨‍🏫 Loading V6 Original Teacher Model...")
    
    model = get_breakthrough_v6_model(base_channels=32)
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    
    if isinstance(checkpoint, dict):
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        elif 'state_dict' in checkpoint:
            model.load_state_dict(checkpoint['state_dict'])
        else:
            model.load_state_dict(checkpoint)
    else:
        model.load_state_dict(checkpoint)
    
    model.eval()
    model.to(device)
    
    params = sum(p.numel() for p in model.parameters())
    size_mb = sum(p.numel() * p.element_size() for p in model.parameters()) / (1024 * 1024)
    
    print(f"✅ V6 Original loaded: {params:,} parameters, {size_mb:.2f}MB")
    return model

def load_ultimate_v2_breakthrough(model_path, device):
    """Load the Ultimate V2 Breakthrough (V3 distilled) model"""
    print("🚀 Loading Ultimate V2 Breakthrough Model...")
    
    model = UltimateBreakthroughV2()
    model.load_state_dict(torch.load(model_path, map_location=device, weights_only=True))
    model.eval()
    model.to(device)
    
    params = sum(p.numel() for p in model.parameters())
    size_mb = sum(p.numel() * p.element_size() for p in model.parameters()) / (1024 * 1024)
    
    print(f"✅ Ultimate V2 loaded: {params:,} parameters, {size_mb:.2f}MB")
    return model

def preprocess_image(image_path, target_size=256):
    """Preprocess image for model inference"""
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")
    
    # Convert BGR to RGB
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    original_shape = image_rgb.shape[:2]
    
    # Resize to target size
    image_resized = cv2.resize(image_rgb, (target_size, target_size))
    
    # Normalize to [0, 1]
    image_normalized = image_resized.astype(np.float32) / 255.0
    
    # Convert to tensor and add batch dimension
    image_tensor = torch.from_numpy(image_normalized).permute(2, 0, 1).unsqueeze(0)
    
    return image_tensor, image_rgb, original_shape

def run_inference(model, image_tensor, device, model_name, num_warmup=3, num_runs=10):
    """Run inference with timing"""
    image_tensor = image_tensor.to(device)
    
    # Warmup runs
    print(f"🔥 Warming up {model_name}...")
    with torch.no_grad():
        for _ in range(num_warmup):
            _ = model(image_tensor)
    
    # Timed runs
    print(f"⏱️ Running {num_runs} timed inferences for {model_name}...")
    times = []
    
    with torch.no_grad():
        for i in range(num_runs):
            start_time = time.perf_counter()
            output = model(image_tensor)
            end_time = time.perf_counter()
            
            inference_time = (end_time - start_time) * 1000  # Convert to ms
            times.append(inference_time)
            
            if i == 0:  # Save first output for visualization
                final_output = output.cpu()
    
    avg_time = np.mean(times)
    std_time = np.std(times)
    min_time = np.min(times)
    max_time = np.max(times)
    
    print(f"📊 {model_name} Timing Results:")
    print(f"   Average: {avg_time:.2f}ms ± {std_time:.2f}ms")
    print(f"   Range: {min_time:.2f}ms - {max_time:.2f}ms")
    
    return final_output, avg_time, times

def postprocess_output(output, original_shape):
    """Postprocess model output to create mask"""
    # Apply sigmoid to get probabilities
    mask = torch.sigmoid(output).squeeze().numpy()
    
    # Resize back to original image size
    mask_resized = cv2.resize(mask, (original_shape[1], original_shape[0]))
    
    # Apply threshold
    mask_binary = (mask_resized > 0.5).astype(np.uint8) * 255
    
    return mask_resized, mask_binary

def calculate_dice_score(mask1, mask2):
    """Calculate Dice score between two masks"""
    intersection = np.sum(mask1 * mask2)
    union = np.sum(mask1) + np.sum(mask2)

    if union == 0:
        return 1.0  # Both masks are empty

    dice = (2.0 * intersection) / union
    return dice

def create_comparison_visualization(original_image, v6_mask, v2_mask, v6_binary, v2_binary,
                                  v6_time, v2_time, image_path):
    """Create comprehensive comparison visualization"""
    fig, axes = plt.subplots(2, 4, figsize=(20, 10))
    fig.suptitle(f'V6 Original vs Ultimate V2 Breakthrough Comparison\nImage: {os.path.basename(image_path)}',
                 fontsize=16, fontweight='bold')

    # Row 1: V6 Original Results
    axes[0, 0].imshow(original_image)
    axes[0, 0].set_title('Original Image', fontweight='bold')
    axes[0, 0].axis('off')

    axes[0, 1].imshow(v6_mask, cmap='hot', alpha=0.8)
    axes[0, 1].set_title(f'V6 Original Heatmap\nTime: {v6_time:.2f}ms', fontweight='bold')
    axes[0, 1].axis('off')

    axes[0, 2].imshow(v6_binary, cmap='gray')
    axes[0, 2].set_title('V6 Binary Mask', fontweight='bold')
    axes[0, 2].axis('off')

    # Overlay V6 mask on original
    overlay_v6 = original_image.copy()
    overlay_v6[v6_binary > 0] = [255, 0, 0]  # Red overlay
    axes[0, 3].imshow(overlay_v6)
    axes[0, 3].set_title('V6 Overlay (Red)', fontweight='bold')
    axes[0, 3].axis('off')

    # Row 2: Ultimate V2 Results
    axes[1, 0].imshow(original_image)
    axes[1, 0].set_title('Original Image', fontweight='bold')
    axes[1, 0].axis('off')

    axes[1, 1].imshow(v2_mask, cmap='hot', alpha=0.8)
    axes[1, 1].set_title(f'Ultimate V2 Heatmap\nTime: {v2_time:.2f}ms', fontweight='bold')
    axes[1, 1].axis('off')

    axes[1, 2].imshow(v2_binary, cmap='gray')
    axes[1, 2].set_title('Ultimate V2 Binary Mask', fontweight='bold')
    axes[1, 2].axis('off')

    # Overlay V2 mask on original
    overlay_v2 = original_image.copy()
    overlay_v2[v2_binary > 0] = [0, 255, 0]  # Green overlay
    axes[1, 3].imshow(overlay_v2)
    axes[1, 3].set_title('Ultimate V2 Overlay (Green)', fontweight='bold')
    axes[1, 3].axis('off')

    plt.tight_layout()

    # Save visualization
    output_path = f"model_comparison_{os.path.splitext(os.path.basename(image_path))[0]}.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"📸 Comparison visualization saved: {output_path}")

    plt.show()
    return output_path

def create_side_by_side_overlay(original_image, v6_binary, v2_binary, image_path):
    """Create side-by-side overlay comparison"""
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle(f'Model Overlay Comparison - {os.path.basename(image_path)}',
                 fontsize=14, fontweight='bold')

    # Original image
    axes[0].imshow(original_image)
    axes[0].set_title('Original Image', fontweight='bold')
    axes[0].axis('off')

    # V6 overlay
    overlay_v6 = original_image.copy()
    overlay_v6[v6_binary > 0] = [255, 0, 0]  # Red
    axes[1].imshow(overlay_v6)
    axes[1].set_title('V6 Original (Red)', fontweight='bold')
    axes[1].axis('off')

    # V2 overlay
    overlay_v2 = original_image.copy()
    overlay_v2[v2_binary > 0] = [0, 255, 0]  # Green
    axes[2].imshow(overlay_v2)
    axes[2].set_title('Ultimate V2 (Green)', fontweight='bold')
    axes[2].axis('off')

    plt.tight_layout()

    # Save side-by-side
    output_path = f"side_by_side_{os.path.splitext(os.path.basename(image_path))[0]}.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"📸 Side-by-side comparison saved: {output_path}")

    plt.show()
    return output_path

def print_detailed_comparison(v6_params, v2_params, v6_size, v2_size, v6_time, v2_time,
                            dice_score, image_path):
    """Print detailed comparison results"""
    print("\n" + "="*80)
    print("🏆 DETAILED MODEL COMPARISON RESULTS")
    print("="*80)
    print(f"📁 Test Image: {os.path.basename(image_path)}")
    print()

    print("📊 MODEL SPECIFICATIONS:")
    print(f"   V6 Original:     {v6_params:,} parameters, {v6_size:.2f}MB")
    print(f"   Ultimate V2:     {v2_params:,} parameters, {v2_size:.2f}MB")

    param_reduction = ((v6_params - v2_params) / v6_params) * 100
    size_reduction = ((v6_size - v2_size) / v6_size) * 100
    print(f"   Parameter Reduction: {param_reduction:.1f}%")
    print(f"   Size Reduction: {size_reduction:.1f}%")
    print()

    print("⚡ PERFORMANCE COMPARISON:")
    print(f"   V6 Original:     {v6_time:.2f}ms")
    print(f"   Ultimate V2:     {v2_time:.2f}ms")

    if v6_time > 0:
        speedup = v6_time / v2_time
        improvement = ((v6_time - v2_time) / v6_time) * 100
        print(f"   Speedup:         {speedup:.2f}x")
        print(f"   Time Improvement: {improvement:.1f}%")
    print()

    print("🎯 ACCURACY COMPARISON:")
    print(f"   Dice Score (similarity): {dice_score:.4f}")
    if dice_score > 0.95:
        print("   ✅ Excellent agreement between models")
    elif dice_score > 0.90:
        print("   ✅ Very good agreement between models")
    elif dice_score > 0.80:
        print("   ⚠️ Good agreement between models")
    else:
        print("   ❌ Significant differences between models")
    print()

    print("🏆 OVERALL ASSESSMENT:")
    if param_reduction > 80 and speedup > 1.5 and dice_score > 0.90:
        print("   🎉 BREAKTHROUGH SUCCESS! Ultimate V2 significantly outperforms V6")
        print("   ✅ Massive size reduction with speed improvement and maintained accuracy")
    elif param_reduction > 50 and speedup > 1.2:
        print("   ✅ SUCCESS! Ultimate V2 provides good improvements over V6")
    else:
        print("   ⚠️ Mixed results - further optimization may be needed")
    print("="*80)

def main():
    """Main comparison function"""
    # Configuration
    image_path = r"C:\Users\<USER>\OneDrive\Desktop\New folder (4)\24.jpg"
    v6_model_path = "best_model.pth"
    v2_model_path = "ultimate_v2_breakthrough/ultimate_v2_breakthrough.pth"

    # Check if files exist
    if not os.path.exists(image_path):
        print(f"❌ Image not found: {image_path}")
        return

    if not os.path.exists(v6_model_path):
        print(f"❌ V6 model not found: {v6_model_path}")
        return

    if not os.path.exists(v2_model_path):
        print(f"❌ Ultimate V2 model not found: {v2_model_path}")
        return

    # Setup device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Using device: {device}")
    print()

    try:
        # Load models
        v6_model = load_v6_original(v6_model_path, device)
        v2_model = load_ultimate_v2_breakthrough(v2_model_path, device)

        # Get model specs
        v6_params = sum(p.numel() for p in v6_model.parameters())
        v2_params = sum(p.numel() for p in v2_model.parameters())
        v6_size = sum(p.numel() * p.element_size() for p in v6_model.parameters()) / (1024 * 1024)
        v2_size = sum(p.numel() * p.element_size() for p in v2_model.parameters()) / (1024 * 1024)

        # Preprocess image
        print("🖼️ Preprocessing image...")
        image_tensor, original_image, original_shape = preprocess_image(image_path)

        # Run inference on both models
        print("\n🚀 Running V6 Original inference...")
        v6_output, v6_time, v6_times = run_inference(v6_model, image_tensor, device, "V6 Original")

        print("\n🚀 Running Ultimate V2 inference...")
        v2_output, v2_time, v2_times = run_inference(v2_model, image_tensor, device, "Ultimate V2")

        # Postprocess outputs
        print("\n📊 Processing results...")
        v6_mask, v6_binary = postprocess_output(v6_output, original_shape)
        v2_mask, v2_binary = postprocess_output(v2_output, original_shape)

        # Calculate similarity
        dice_score = calculate_dice_score(v6_binary > 0, v2_binary > 0)

        # Create visualizations
        print("\n🎨 Creating visualizations...")
        comparison_path = create_comparison_visualization(
            original_image, v6_mask, v2_mask, v6_binary, v2_binary,
            v6_time, v2_time, image_path
        )

        side_by_side_path = create_side_by_side_overlay(
            original_image, v6_binary, v2_binary, image_path
        )

        # Print detailed results
        print_detailed_comparison(
            v6_params, v2_params, v6_size, v2_size, v6_time, v2_time,
            dice_score, image_path
        )

        print(f"\n📸 Visualizations saved:")
        print(f"   - {comparison_path}")
        print(f"   - {side_by_side_path}")

    except Exception as e:
        print(f"❌ Error during comparison: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
