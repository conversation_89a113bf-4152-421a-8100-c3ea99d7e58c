"""
CPU-Optimized V6 Converter
Fair comparison between PyTorch and ONNX on CPU with optimizations
"""

import torch
import torch.nn as nn
import numpy as np
import os
import time
from pathlib import Path

# Import the V6 model architecture
from breakthrough_unet_v6_simple import BreakthroughUNetV6, get_breakthrough_v6_model

def load_v6_model(model_path, device):
    """Load the V6 model from checkpoint"""
    print(f"🔬 Loading V6 model from: {model_path}")
    
    # Create model
    model = get_breakthrough_v6_model(base_channels=32)
    
    # Load weights
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    
    # Handle different checkpoint formats
    if isinstance(checkpoint, dict):
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        elif 'state_dict' in checkpoint:
            model.load_state_dict(checkpoint['state_dict'])
        else:
            model.load_state_dict(checkpoint)
    else:
        model.load_state_dict(checkpoint)
    
    model.eval()
    model.to(device)
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    print(f"✅ V6 model loaded: {total_params:,} parameters")
    
    return model

def convert_to_onnx_cpu_optimized(model, output_path, device):
    """Convert to ONNX with CPU optimizations"""
    print(f"🔄 Converting to CPU-optimized ONNX: {output_path}")
    
    model.eval()
    
    # Move to CPU for conversion
    model_cpu = model.cpu()
    dummy_input = torch.randn(1, 3, 256, 256)
    
    # Test model first
    with torch.no_grad():
        test_output = model_cpu(dummy_input)
        print(f"✅ Model test successful, output shape: {test_output.shape}")
    
    # Export with CPU-optimized settings
    torch.onnx.export(
        model_cpu,
        dummy_input,
        output_path,
        export_params=True,
        opset_version=11,  # Stable opset
        do_constant_folding=True,  # Fold constants
        input_names=['input'],
        output_names=['output'],
        verbose=False,
        # CPU-specific optimizations
        training=torch.onnx.TrainingMode.EVAL,
        strip_doc_string=True
    )
    
    print(f"✅ ONNX model saved: {output_path}")
    return output_path

def test_onnx_cpu_optimized(onnx_path):
    """Test ONNX with CPU optimizations"""
    print(f"🧪 Testing CPU-optimized ONNX: {onnx_path}")
    
    try:
        import onnxruntime as ort
        
        # CPU-optimized session options
        sess_options = ort.SessionOptions()
        sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
        sess_options.intra_op_num_threads = 1  # Single thread for vCPU
        sess_options.inter_op_num_threads = 1
        sess_options.execution_mode = ort.ExecutionMode.ORT_SEQUENTIAL
        sess_options.enable_mem_pattern = True
        sess_options.enable_cpu_mem_arena = True
        
        # Create session with CPU provider
        session = ort.InferenceSession(
            onnx_path, 
            sess_options, 
            providers=['CPUExecutionProvider']
        )
        
        # Test inference
        input_name = session.get_inputs()[0].name
        test_input = np.random.randn(1, 3, 256, 256).astype(np.float32)
        
        # Warmup
        for _ in range(5):
            _ = session.run(None, {input_name: test_input})
        
        # Benchmark
        times = []
        for _ in range(20):
            start_time = time.time()
            _ = session.run(None, {input_name: test_input})
            end_time = time.time()
            times.append((end_time - start_time) * 1000)
        
        avg_time = np.mean(times)
        std_time = np.std(times)
        print(f"✅ ONNX CPU inference: {avg_time:.2f} ± {std_time:.2f} ms (20 runs)")
        
        return True, avg_time
        
    except Exception as e:
        print(f"❌ ONNX test failed: {e}")
        return False, 0

def benchmark_pytorch_cpu(model):
    """Benchmark PyTorch model on CPU"""
    print("📊 Benchmarking PyTorch on CPU...")
    
    # Move model to CPU
    model_cpu = model.cpu()
    model_cpu.eval()
    
    test_input = torch.randn(1, 3, 256, 256)
    
    # Warmup
    with torch.no_grad():
        for _ in range(5):
            _ = model_cpu(test_input)
    
    # Benchmark
    times = []
    with torch.no_grad():
        for _ in range(20):
            start_time = time.time()
            _ = model_cpu(test_input)
            end_time = time.time()
            times.append((end_time - start_time) * 1000)
    
    avg_time = np.mean(times)
    std_time = np.std(times)
    print(f"✅ PyTorch CPU inference: {avg_time:.2f} ± {std_time:.2f} ms (20 runs)")
    
    return avg_time

def create_simplified_v6(original_model):
    """Create a simplified version of V6 for better ONNX compatibility"""
    print("🔧 Creating simplified V6 model...")
    
    class SimplifiedV6(nn.Module):
        def __init__(self, base_channels=16):
            super(SimplifiedV6, self).__init__()
            
            # Simplified encoder
            c1, c2, c3, c4 = base_channels, base_channels*2, base_channels*4, base_channels*8
            
            self.inc = nn.Sequential(
                nn.Conv2d(3, c1, 3, padding=1),
                nn.BatchNorm2d(c1),
                nn.ReLU(inplace=True),
                nn.Conv2d(c1, c1, 3, padding=1),
                nn.BatchNorm2d(c1),
                nn.ReLU(inplace=True)
            )
            
            self.down1 = nn.Sequential(
                nn.MaxPool2d(2),
                nn.Conv2d(c1, c2, 3, padding=1),
                nn.BatchNorm2d(c2),
                nn.ReLU(inplace=True)
            )
            
            self.down2 = nn.Sequential(
                nn.MaxPool2d(2),
                nn.Conv2d(c2, c3, 3, padding=1),
                nn.BatchNorm2d(c3),
                nn.ReLU(inplace=True)
            )
            
            self.down3 = nn.Sequential(
                nn.MaxPool2d(2),
                nn.Conv2d(c3, c4, 3, padding=1),
                nn.BatchNorm2d(c4),
                nn.ReLU(inplace=True)
            )
            
            # Simplified decoder
            self.up3 = nn.ConvTranspose2d(c4, c3, 2, stride=2)
            self.conv3 = nn.Sequential(
                nn.Conv2d(c4, c3, 3, padding=1),
                nn.BatchNorm2d(c3),
                nn.ReLU(inplace=True)
            )
            
            self.up2 = nn.ConvTranspose2d(c3, c2, 2, stride=2)
            self.conv2 = nn.Sequential(
                nn.Conv2d(c3, c2, 3, padding=1),
                nn.BatchNorm2d(c2),
                nn.ReLU(inplace=True)
            )
            
            self.up1 = nn.ConvTranspose2d(c2, c1, 2, stride=2)
            self.conv1 = nn.Sequential(
                nn.Conv2d(c2, c1, 3, padding=1),
                nn.BatchNorm2d(c1),
                nn.ReLU(inplace=True)
            )
            
            self.outc = nn.Conv2d(c1, 1, 1)
            
        def forward(self, x):
            # Encoder
            x1 = self.inc(x)
            x2 = self.down1(x1)
            x3 = self.down2(x2)
            x4 = self.down3(x3)
            
            # Decoder
            x = self.up3(x4)
            x = torch.cat([x, x3], dim=1)
            x = self.conv3(x)
            
            x = self.up2(x)
            x = torch.cat([x, x2], dim=1)
            x = self.conv2(x)
            
            x = self.up1(x)
            x = torch.cat([x, x1], dim=1)
            x = self.conv1(x)
            
            return self.outc(x)
    
    simplified = SimplifiedV6(base_channels=16)
    
    # Count parameters
    total_params = sum(p.numel() for p in simplified.parameters())
    print(f"✅ Simplified V6 created: {total_params:,} parameters")
    
    return simplified

def cpu_optimized_conversion(model_path="best_model.pth", output_dir="optimized_models"):
    """CPU-optimized V6 conversion with fair comparison"""
    print("🚀 Starting CPU-Optimized V6 Conversion")
    print("=" * 50)
    
    # Use CPU for fair comparison
    device = torch.device('cpu')
    print(f"🖥️ Using device: {device} (for fair comparison)")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # Step 1: Load original model
        original_model = load_v6_model(model_path, device)
        
        # Step 2: Benchmark original PyTorch on CPU
        pytorch_time = benchmark_pytorch_cpu(original_model)
        
        # Step 3: Convert original to ONNX
        onnx_path = os.path.join(output_dir, "v6_cpu_optimized.onnx")
        convert_to_onnx_cpu_optimized(original_model, onnx_path, device)
        
        # Step 4: Test ONNX
        onnx_success, onnx_time = test_onnx_cpu_optimized(onnx_path)
        
        # Step 5: Create simplified model
        simplified_model = create_simplified_v6(original_model)
        
        # Step 6: Convert simplified to ONNX
        simplified_onnx_path = os.path.join(output_dir, "v6_simplified.onnx")
        convert_to_onnx_cpu_optimized(simplified_model, simplified_onnx_path, device)
        
        # Step 7: Test simplified ONNX
        simplified_success, simplified_time = test_onnx_cpu_optimized(simplified_onnx_path)
        
        # Step 8: Calculate results
        if onnx_success and onnx_time > 0:
            speedup = pytorch_time / onnx_time
        else:
            speedup = 0
            
        if simplified_success and simplified_time > 0:
            simplified_speedup = pytorch_time / simplified_time
        else:
            simplified_speedup = 0
        
        # Step 9: File sizes
        original_size = os.path.getsize(model_path) / (1024 * 1024)  # MB
        onnx_size = os.path.getsize(onnx_path) / (1024 * 1024)  # MB
        simplified_size = os.path.getsize(simplified_onnx_path) / (1024 * 1024)  # MB
        
        print("\n📊 CPU OPTIMIZATION RESULTS:")
        print("=" * 50)
        print(f"Original PyTorch (CPU): {pytorch_time:.2f} ms")
        print(f"Original ONNX (CPU): {onnx_time:.2f} ms - {speedup:.2f}x")
        print(f"Simplified ONNX (CPU): {simplified_time:.2f} ms - {simplified_speedup:.2f}x")
        print()
        print(f"Original model size: {original_size:.2f} MB")
        print(f"Original ONNX size: {onnx_size:.2f} MB")
        print(f"Simplified ONNX size: {simplified_size:.2f} MB")
        print(f"Size reduction (simplified): {(1 - simplified_size/original_size)*100:.1f}%")
        
        return {
            'pytorch_cpu_time': pytorch_time,
            'onnx_time': onnx_time,
            'simplified_time': simplified_time,
            'speedup': speedup,
            'simplified_speedup': simplified_speedup,
            'original_size_mb': original_size,
            'onnx_size_mb': onnx_size,
            'simplified_size_mb': simplified_size,
            'onnx_path': onnx_path,
            'simplified_path': simplified_onnx_path
        }
        
    except Exception as e:
        print(f"❌ Conversion failed: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    # Run CPU-optimized conversion
    results = cpu_optimized_conversion(
        model_path="best_model.pth",
        output_dir="optimized_models"
    )
    
    print("\n🎉 CPU-Optimized Conversion Results:")
    print("=" * 50)
    for key, value in results.items():
        if isinstance(value, float):
            print(f"{key}: {value:.2f}")
        else:
            print(f"{key}: {value}")
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS:")
    if results.get('simplified_speedup', 0) > 1.5:
        print("🚀 Use simplified ONNX model - significant speedup!")
    elif results.get('speedup', 0) > 1.2:
        print("✅ Use original ONNX model - good speedup")
    else:
        print("⚠️ Stick with PyTorch - ONNX not faster on this hardware")
