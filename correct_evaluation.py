"""
Corrected Evaluation for Breakthrough Distillation
Fix the evaluation issues and get accurate performance metrics
"""

import torch
import torch.nn.functional as F
import numpy as np
import time
from pathlib import Path
from torch.utils.data import DataLoader

# Import components
from breakthrough_distillation import (
    ValAugmentedChessDataset,
    load_teacher_model,
    BreakthroughStudentV6
)

def correct_performance_evaluation(teacher, student, device, dataset_path="augmented_20250518_153326"):
    """Corrected evaluation with proper metrics"""
    print("📊 CORRECTED Breakthrough Model Evaluation...")
    
    # Use validation dataset
    val_dataset = ValAugmentedChessDataset(dataset_path, split_ratio=0.85)
    val_loader = DataLoader(val_dataset, batch_size=1, shuffle=False, num_workers=0)
    
    teacher.eval()
    student.eval()
    
    total_mse = 0
    total_mae = 0
    max_diff = 0
    num_samples = 0
    
    teacher_outputs = []
    student_outputs = []
    
    print(f"Evaluating on {len(val_dataset)} validation samples...")
    
    with torch.no_grad():
        for batch_idx, (images, masks) in enumerate(val_loader):
            images = images.to(device)
            
            # Get teacher and student outputs
            teacher_output = teacher(images)
            student_output = student(images)
            
            # Store for analysis
            teacher_outputs.append(teacher_output.cpu())
            student_outputs.append(student_output.cpu())
            
            # Calculate metrics
            mse = F.mse_loss(student_output, teacher_output).item()
            mae = F.l1_loss(student_output, teacher_output).item()
            max_diff_sample = torch.abs(student_output - teacher_output).max().item()
            
            total_mse += mse
            total_mae += mae
            max_diff = max(max_diff, max_diff_sample)
            num_samples += 1
            
            if batch_idx % 5 == 0:
                print(f"  Sample {batch_idx+1}: MSE={mse:.2f}, MAE={mae:.2f}")
    
    avg_mse = total_mse / num_samples
    avg_mae = total_mae / num_samples
    
    # Calculate proper performance difference
    # Use teacher's output variance as baseline
    teacher_tensor = torch.cat(teacher_outputs, dim=0)
    teacher_variance = torch.var(teacher_tensor).item()
    
    # Performance difference as percentage of teacher's capability
    performance_diff_percent = (avg_mse / teacher_variance) * 100
    
    print(f"\n✅ CORRECTED Evaluation Results:")
    print(f"   Average MSE: {avg_mse:.6f}")
    print(f"   Average MAE: {avg_mae:.6f}")
    print(f"   Max Difference: {max_diff:.6f}")
    print(f"   Teacher Variance: {teacher_variance:.6f}")
    print(f"   Performance Difference: {performance_diff_percent:.2f}%")
    print(f"   Evaluated on {num_samples} samples")
    
    # Quality assessment
    if performance_diff_percent < 1.0:
        print("🎉 BREAKTHROUGH SUCCESS! <1% performance difference!")
        success = True
    elif performance_diff_percent < 2.0:
        print("🥇 Excellent! <2% performance difference")
        success = True
    elif performance_diff_percent < 5.0:
        print("✅ Very good! <5% performance difference")
        success = False
    else:
        print("⚠️ Performance gap detected")
        success = False
    
    return avg_mse, avg_mae, max_diff, performance_diff_percent, success

def correct_speed_benchmark(teacher, student, device, num_runs=50):
    """Corrected speed benchmarking"""
    print("🏃‍♂️ CORRECTED Speed Benchmarking...")
    
    # Create dummy input
    dummy_input = torch.randn(1, 3, 256, 256).to(device)
    
    teacher.eval()
    student.eval()
    
    # Warmup
    for _ in range(10):
        with torch.no_grad():
            _ = teacher(dummy_input)
            _ = student(dummy_input)
    
    # Benchmark teacher
    teacher_times = []
    for _ in range(num_runs):
        start_time = time.time()
        with torch.no_grad():
            _ = teacher(dummy_input)
        torch.cuda.synchronize() if device.type == 'cuda' else None
        teacher_times.append((time.time() - start_time) * 1000)
    
    # Benchmark student
    student_times = []
    for _ in range(num_runs):
        start_time = time.time()
        with torch.no_grad():
            _ = student(dummy_input)
        torch.cuda.synchronize() if device.type == 'cuda' else None
        student_times.append((time.time() - start_time) * 1000)
    
    teacher_avg = np.mean(teacher_times)
    teacher_std = np.std(teacher_times)
    student_avg = np.mean(student_times)
    student_std = np.std(student_times)
    speedup = teacher_avg / student_avg
    
    print(f"   Teacher: {teacher_avg:.2f} ± {teacher_std:.2f} ms")
    print(f"   Student: {student_avg:.2f} ± {student_std:.2f} ms")
    print(f"   Speedup: {speedup:.2f}x")
    
    return teacher_avg, student_avg, speedup

def run_corrected_evaluation():
    """Run complete corrected evaluation"""
    print("🔧 CORRECTED BREAKTHROUGH EVALUATION")
    print("=" * 50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Using device: {device}")
    
    try:
        # Load teacher
        teacher = load_teacher_model("best_model.pth", device)
        
        # Load student
        student = BreakthroughStudentV6(base_channels=16)
        student.load_state_dict(torch.load('breakthrough_student_v6.pth', weights_only=True))
        student.to(device)
        
        print(f"✅ Models loaded successfully")
        
        # Corrected evaluation
        mse, mae, max_diff, perf_diff, success = correct_performance_evaluation(
            teacher, student, device
        )
        
        # Corrected speed benchmark
        teacher_time, student_time, speedup = correct_speed_benchmark(
            teacher, student, device
        )
        
        # Calculate parameters
        teacher_params = sum(p.numel() for p in teacher.parameters())
        student_params = sum(p.numel() for p in student.parameters())
        param_reduction = (1 - student_params/teacher_params) * 100
        
        print("\n🎉 CORRECTED FINAL RESULTS:")
        print("=" * 40)
        print(f"🎯 Performance Difference: {perf_diff:.2f}%")
        
        if success:
            print("🏆 BREAKTHROUGH SUCCESS ACHIEVED!")
        else:
            print("🥈 Excellent performance achieved!")
        
        print(f"\nModel Comparison:")
        print(f"  Teacher parameters: {teacher_params:,}")
        print(f"  Student parameters: {student_params:,}")
        print(f"  Parameter reduction: {param_reduction:.1f}%")
        
        print(f"\nPerformance:")
        print(f"  Teacher time: {teacher_time:.2f} ms")
        print(f"  Student time: {student_time:.2f} ms")
        print(f"  Speedup: {speedup:.2f}x")
        
        print(f"\nAccuracy:")
        print(f"  MSE: {mse:.6f}")
        print(f"  MAE: {mae:.6f}")
        print(f"  Max difference: {max_diff:.6f}")
        
        # Final assessment
        print(f"\n🎯 BREAKTHROUGH ASSESSMENT:")
        if perf_diff < 1.0:
            print("🏆 <1% performance difference - BREAKTHROUGH TARGET ACHIEVED!")
        elif perf_diff < 2.0:
            print("🥇 <2% performance difference - Excellent result!")
        elif perf_diff < 5.0:
            print("✅ <5% performance difference - Very good result!")
        
        print(f"\n🚀 Production Readiness:")
        print(f"  Speed improvement: {speedup:.1f}x faster")
        print(f"  Model size: {param_reduction:.1f}% smaller")
        print(f"  Accuracy: {100-perf_diff:.1f}% of teacher performance")
        print(f"  Deployment ready: {'✅ YES' if perf_diff < 5 else '⚠️ NEEDS WORK'}")
        
        return {
            'performance_difference': perf_diff,
            'breakthrough_success': success,
            'speedup': speedup,
            'parameter_reduction': param_reduction,
            'mse': mse,
            'mae': mae,
            'max_diff': max_diff
        }
        
    except Exception as e:
        print(f"❌ Evaluation failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    results = run_corrected_evaluation()
    
    if results:
        print(f"\n📋 SUMMARY:")
        print(f"Performance difference: {results['performance_difference']:.2f}%")
        print(f"Breakthrough success: {results['breakthrough_success']}")
        print(f"Speedup: {results['speedup']:.2f}x")
        print(f"Parameter reduction: {results['parameter_reduction']:.1f}%")
    else:
        print("❌ Evaluation failed - check logs above")
