"""
Complete Chess FEN Generation API
Full implementation of generate_fen_v6_geometric.py for Hugging Face Spaces
"""

import os
import sys
import cv2
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from ultralytics import YOLO
import gradio as gr
import time
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
CONFIG = {
    "fen_symbols": {
        "white_pawn": "P", "white_knight": "N", "white_bishop": "B", 
        "white_rook": "R", "white_queen": "Q", "white_king": "K",
        "black_pawn": "p", "black_knight": "n", "black_bishop": "b", 
        "black_rook": "r", "black_queen": "q", "black_king": "k"
    }
}

# V6 Model Architecture (copied from breakthrough_unet_v6_simple.py)
class DoubleConv(nn.Module):
    """Double convolution block"""
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.double_conv = nn.Sequential(
            nn.Conv2d(in_channels, out_channels, 3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, 3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )

    def forward(self, x):
        return self.double_conv(x)

class Down(nn.Module):
    """Downscaling with maxpool then double conv"""
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.maxpool_conv = nn.Sequential(
            nn.MaxPool2d(2),
            DoubleConv(in_channels, out_channels)
        )

    def forward(self, x):
        return self.maxpool_conv(x)

class Up(nn.Module):
    """Upscaling then double conv"""
    def __init__(self, in_channels, out_channels, bilinear=True):
        super().__init__()
        if bilinear:
            self.up = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
            self.conv = DoubleConv(in_channels, out_channels)
        else:
            self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, 2, stride=2)
            self.conv = DoubleConv(in_channels, out_channels)

    def forward(self, x1, x2):
        x1 = self.up(x1)
        diffY = x2.size()[2] - x1.size()[2]
        diffX = x2.size()[3] - x1.size()[3]
        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2, diffY // 2, diffY - diffY // 2])
        x = torch.cat([x2, x1], dim=1)
        return self.conv(x)

class OutConv(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(OutConv, self).__init__()
        self.conv = nn.Conv2d(in_channels, out_channels, 1)

    def forward(self, x):
        return self.conv(x)

class BreakthroughUNetV6(nn.Module):
    """Breakthrough U-Net V6 model"""
    def __init__(self, n_channels=3, n_classes=1, base_channels=32, bilinear=True):
        super(BreakthroughUNetV6, self).__init__()
        self.n_channels = n_channels
        self.n_classes = n_classes
        self.bilinear = bilinear

        self.inc = DoubleConv(n_channels, base_channels)
        self.down1 = Down(base_channels, base_channels * 2)
        self.down2 = Down(base_channels * 2, base_channels * 4)
        self.down3 = Down(base_channels * 4, base_channels * 8)
        factor = 2 if bilinear else 1
        self.down4 = Down(base_channels * 8, base_channels * 16 // factor)
        self.up1 = Up(base_channels * 16, base_channels * 8 // factor, bilinear)
        self.up2 = Up(base_channels * 8, base_channels * 4 // factor, bilinear)
        self.up3 = Up(base_channels * 4, base_channels * 2 // factor, bilinear)
        self.up4 = Up(base_channels * 2, base_channels, bilinear)
        self.outc = OutConv(base_channels, n_classes)

    def forward(self, x):
        x1 = self.inc(x)
        x2 = self.down1(x1)
        x3 = self.down2(x2)
        x4 = self.down3(x3)
        x5 = self.down4(x4)
        x = self.up1(x5, x4)
        x = self.up2(x, x3)
        x = self.up3(x, x2)
        x = self.up4(x, x1)
        logits = self.outc(x)
        return logits

def get_breakthrough_v6_model(base_channels=32):
    """Get the breakthrough V6 model"""
    return BreakthroughUNetV6(n_channels=3, n_classes=1, base_channels=base_channels)

def load_v6_model():
    """Load V6 model for Hugging Face environment"""
    try:
        model = get_breakthrough_v6_model(base_channels=32)
        
        # Load the model weights
        model_path = "best.pt"  # V6 model in HF space
        if os.path.exists(model_path):
            state_dict = torch.load(model_path, map_location='cpu', weights_only=True)
            model.load_state_dict(state_dict)
            logger.info("✅ V6 model loaded successfully")
        else:
            logger.error(f"❌ V6 model file not found: {model_path}")
            return None
        
        model.eval()
        return model
        
    except Exception as e:
        logger.error(f"❌ Failed to load V6 model: {e}")
        return None

def load_yolo_model():
    """Load YOLO model for Hugging Face environment"""
    try:
        model_path = "best_mobile.onnx"  # YOLO model in HF space
        if os.path.exists(model_path):
            model = YOLO(model_path)
            logger.info("✅ YOLO model loaded successfully")
            return model
        else:
            logger.error(f"❌ YOLO model file not found: {model_path}")
            return None
            
    except Exception as e:
        logger.error(f"❌ Failed to load YOLO model: {e}")
        return None

def find_board_corners(mask):
    """Find the four corners of the chess board from segmentation mask."""
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    if not contours:
        return None

    largest_contour = max(contours, key=cv2.contourArea)
    epsilon = 0.02 * cv2.arcLength(largest_contour, True)
    approx = cv2.approxPolyDP(largest_contour, epsilon, True)

    if len(approx) != 4:
        x, y, w, h = cv2.boundingRect(largest_contour)
        corners = np.array([
            [x, y], [x + w, y], [x + w, y + h], [x, y + h]
        ], dtype=np.float32)
    else:
        corners = approx.reshape(4, 2).astype(np.float32)
        corners = corners[np.argsort(corners[:, 1])]
        top_corners = corners[:2][np.argsort(corners[:2, 0])]
        bottom_corners = corners[2:][np.argsort(corners[2:, 0])[::-1]]
        corners = np.array([top_corners[0], top_corners[1], bottom_corners[0], bottom_corners[1]], dtype=np.float32)

    return corners

def create_perspective_corrected_board(image, corners, output_size=512):
    """Create perspective-corrected chess board."""
    dst_corners = np.array([
        [0, 0], [output_size - 1, 0],
        [output_size - 1, output_size - 1], [0, output_size - 1]
    ], dtype=np.float32)

    transform_matrix = cv2.getPerspectiveTransform(corners, dst_corners)
    corrected_board = cv2.warpPerspective(image, transform_matrix, (output_size, output_size))

    return corrected_board, transform_matrix

def create_chess_grid(board_size=512):
    """Create 8x8 chess grid."""
    cell_size = board_size / 8

    vertical_lines = []
    horizontal_lines = []

    for i in range(1, 8):
        x = int(i * cell_size)
        y = int(i * cell_size)
        vertical_lines.append([(x, 0), (x, board_size)])
        horizontal_lines.append([(0, y), (board_size, y)])

    squares = []
    for row in range(8):
        for col in range(8):
            x1 = int(col * cell_size)
            y1 = int(row * cell_size)
            x2 = int((col + 1) * cell_size)
            y2 = int((row + 1) * cell_size)

            squares.append({
                'row': row, 'col': col,
                'chess_row': 7 - row, 'chess_col': col,
                'bbox': (x1, y1, x2, y2),
                'center': ((x1 + x2) // 2, (y1 + y2) // 2)
            })

    return vertical_lines, horizontal_lines, squares, cell_size

def detect_chessboard_v6_geometric(model, image_path, device='cpu'):
    """Detect chessboard using V6 and create geometrically correct grid."""
    logger.info(f"🔍 V6 Geometric chess board detection: {image_path}")

    # Load image
    original_image = cv2.imread(image_path)
    if original_image is None:
        logger.error(f"❌ Could not load image from {image_path}")
        return None

    image_rgb = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
    original_h, original_w = image_rgb.shape[:2]

    # Resize for V6 model (256x256 as per original script)
    target_size = 256
    image_resized = cv2.resize(image_rgb, (target_size, target_size))

    # Normalize and convert to tensor
    image_normalized = image_resized.astype(np.float32) / 255.0
    image_tensor = torch.from_numpy(image_normalized).permute(2, 0, 1).unsqueeze(0).to(device)

    # Run V6 inference
    start_time = time.time()
    with torch.no_grad():
        output = model(image_tensor)
        prediction = torch.sigmoid(output)
    inference_time = (time.time() - start_time) * 1000

    # Convert to numpy and resize back
    mask = prediction.cpu().squeeze().numpy()
    binary_mask = (mask > 0.5).astype(np.uint8)
    mask_resized = cv2.resize(binary_mask, (original_w, original_h))

    logger.info(f"✅ V6 inference: {inference_time:.2f}ms, range: [{mask.min():.4f}, {mask.max():.4f}]")

    # Find board corners
    corners = find_board_corners(mask_resized)
    if corners is None:
        return None

    # Create perspective-corrected board
    board_corrected, transform_matrix = create_perspective_corrected_board(original_image, corners)

    # Create chess grid
    v_lines, h_lines, squares, cell_size = create_chess_grid()

    return {
        'original_image': original_image,
        'mask': mask_resized,
        'corners': corners,
        'board_corrected': board_corrected,
        'transform_matrix': transform_matrix,
        'vertical_lines': v_lines,
        'horizontal_lines': h_lines,
        'squares': squares,
        'cell_size': cell_size,
        'inference_time': inference_time
    }

def enhance_board_for_detection(board_image):
    """Apply conservative preprocessing to enhance piece detection."""
    logger.info("🔧 Applying conservative board enhancement...")

    # Convert to RGB if needed
    if len(board_image.shape) == 3:
        board_rgb = cv2.cvtColor(board_image, cv2.COLOR_BGR2RGB)
    else:
        board_rgb = board_image

    # Apply very mild histogram equalization
    lab = cv2.cvtColor(board_rgb, cv2.COLOR_RGB2LAB)
    lab[:,:,0] = cv2.createCLAHE(clipLimit=1.5, tileGridSize=(4,4)).apply(lab[:,:,0])
    enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)

    # Apply very subtle sharpening
    kernel = np.array([[0,-0.5,0], [-0.5,3,-0.5], [0,-0.5,0]])
    sharpened = cv2.filter2D(enhanced, -1, kernel)

    # Blend with more weight on original
    enhanced_final = cv2.addWeighted(enhanced, 0.85, sharpened, 0.15, 0)
    enhanced_final = np.clip(enhanced_final, 0, 255).astype(np.uint8)

    logger.info("✅ Conservative enhancement completed")
    return enhanced_final

def detect_pieces(model, board_image):
    """Detect chess pieces using YOLO model with dual detection."""
    logger.info("🎯 Detecting chess pieces with dual detection...")

    # Create enhanced board
    enhanced_board = enhance_board_for_detection(board_image)

    logger.info("🔍 Running YOLO on original board...")
    # Primary detection on original board
    results_original = model(board_image, imgsz=416, conf=0.5, iou=0.7)[0]

    logger.info("🔍 Running YOLO on enhanced board...")
    # Secondary detection on enhanced board
    results_enhanced = model(enhanced_board, imgsz=416, conf=0.4, iou=0.7)[0]

    # Collect all detections
    all_detections = []
    class_names = results_original.names

    # Add original board detections
    if len(results_original.boxes) > 0:
        boxes = results_original.boxes.xyxy.cpu().numpy()
        scores = results_original.boxes.conf.cpu().numpy()
        class_ids = results_original.boxes.cls.cpu().numpy()

        for box, score, class_id in zip(boxes, scores, class_ids):
            all_detections.append({
                'bbox': box,
                'confidence': score,
                'class_id': int(class_id),
                'class_name': class_names[int(class_id)],
                'source': 'original'
            })

    # Add enhanced board detections
    if len(results_enhanced.boxes) > 0:
        boxes = results_enhanced.boxes.xyxy.cpu().numpy()
        scores = results_enhanced.boxes.conf.cpu().numpy()
        class_ids = results_enhanced.boxes.cls.cpu().numpy()

        for box, score, class_id in zip(boxes, scores, class_ids):
            all_detections.append({
                'bbox': box,
                'confidence': score,
                'class_id': int(class_id),
                'class_name': class_names[int(class_id)],
                'source': 'enhanced'
            })

    # Convert to final format
    pieces = []
    for detection in all_detections:
        x1, y1, x2, y2 = detection['bbox']
        center_x = (x1 + x2) / 2
        center_y = (y1 + y2) / 2

        pieces.append({
            'bbox': detection['bbox'],
            'center': (center_x, center_y),
            'confidence': detection['confidence'],
            'class_id': detection['class_id'],
            'class_name': detection['class_name'],
            'source': detection['source']
        })

    # Sort by confidence (highest first)
    pieces = sorted(pieces, key=lambda p: p['confidence'], reverse=True)
    logger.info(f"✅ Detected {len(pieces)} chess pieces")
    return pieces

def map_pieces_to_geometric_grid(pieces, squares):
    """Map pieces to geometric grid squares based on center point."""
    logger.info("🗺️ Mapping pieces to geometric grid...")

    # Initialize grid
    grid = [[None for _ in range(8)] for _ in range(8)]

    # Sort pieces by confidence
    sorted_pieces = sorted(pieces, key=lambda p: p['confidence'], reverse=True)

    mapped_pieces = 0

    for piece in sorted_pieces:
        piece_center = piece['center']
        best_square = None

        # Find which square contains the piece center
        for square in squares:
            x1, y1, x2, y2 = square['bbox']
            if x1 <= piece_center[0] <= x2 and y1 <= piece_center[1] <= y2:
                best_square = square
                break

        if best_square:
            chess_row = best_square['chess_row']
            chess_col = best_square['chess_col']

            # Place piece if square is empty
            if grid[chess_row][chess_col] is None:
                piece_with_pos = piece.copy()
                piece_with_pos['square'] = best_square
                grid[chess_row][chess_col] = piece_with_pos
                mapped_pieces += 1

                # Convert to chess notation
                file_letter = chr(ord('a') + chess_col)
                rank_number = chess_row + 1
                logger.info(f"   🎯 Mapped {piece['class_name']} to {file_letter}{rank_number} (conf: {piece['confidence']:.3f})")

    logger.info(f"✅ Mapped {mapped_pieces}/{len(pieces)} pieces to grid")
    return grid

def generate_fen(grid):
    """Generate FEN notation from piece grid."""
    logger.info("📝 Generating FEN notation...")

    fen_rows = []

    # FEN notation starts from rank 8 (index 7 in our grid) and goes to rank 1 (index 0)
    for chess_rank in range(7, -1, -1):  # 7, 6, 5, 4, 3, 2, 1, 0 (rank 8 to rank 1)
        empty_count = 0
        row_fen = ""

        # Iterate through files a-h (left to right)
        for chess_file in range(8):  # 0 to 7 (files a to h)
            square = grid[chess_rank][chess_file]

            if square is None:
                empty_count += 1
            else:
                if empty_count > 0:
                    row_fen += str(empty_count)
                    empty_count = 0

                class_name = square['class_name']
                if class_name in CONFIG["fen_symbols"]:
                    row_fen += CONFIG["fen_symbols"][class_name]
                else:
                    logger.warning(f"⚠️ Unknown piece: {class_name}")
                    row_fen += "?"

        if empty_count > 0:
            row_fen += str(empty_count)

        fen_rows.append(row_fen)

    # Join ranks with '/'
    board_fen = "/".join(fen_rows)

    # Add default game state (assuming white to move, all castling available, no en passant, halfmove 0, fullmove 1)
    full_fen = f"{board_fen} w KQkq - 0 1"

    logger.info(f"✅ Generated FEN: {full_fen}")
    return full_fen

def process_chess_image(image_path):
    """Main processing function - exactly as per original script."""
    try:
        start_time = time.time()
        logger.info(f"🚀 Processing chess image: {image_path}")

        # Stage 1: V6 Board Detection
        logger.info("🔍 Stage 1: V6 Board Detection...")
        board_results = detect_chessboard_v6_geometric(v6_model, image_path, device='cpu')

        if board_results is None:
            return {
                "success": False,
                "error": "Board detection failed",
                "fen": None,
                "processing_time": 0
            }

        # Stage 2: Piece Detection
        logger.info("🎯 Stage 2: Piece Detection...")
        pieces = detect_pieces(yolo_model, board_results['board_corrected'])

        # Stage 3: Grid Mapping
        logger.info("🗺️ Stage 3: Grid Mapping...")
        grid = map_pieces_to_geometric_grid(pieces, board_results['squares'])

        # Stage 4: FEN Generation
        logger.info("📝 Stage 4: FEN Generation...")
        fen = generate_fen(grid)

        total_time = time.time() - start_time
        logger.info(f"✅ Processing completed in {total_time:.2f}s")

        return {
            "success": True,
            "fen": fen,
            "pieces_detected": len(pieces),
            "pieces_mapped": sum(1 for row in grid for cell in row if cell is not None),
            "processing_time": total_time,
            "v6_inference_time": board_results['inference_time']
        }

    except Exception as e:
        logger.error(f"❌ Processing failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "fen": None,
            "processing_time": 0
        }

def gradio_interface(image):
    """Gradio interface function."""
    if image is None:
        return "Please upload an image."

    # Save uploaded image temporarily
    temp_path = "temp_chess_image.jpg"
    cv2.imwrite(temp_path, cv2.cvtColor(image, cv2.COLOR_RGB2BGR))

    # Process the image
    result = process_chess_image(temp_path)

    # Clean up
    if os.path.exists(temp_path):
        os.remove(temp_path)

    if result["success"]:
        return f"""✅ **Chess FEN Generated Successfully!**

**FEN Notation:** `{result['fen']}`

**Processing Details:**
- 🎯 Pieces Detected: {result['pieces_detected']}
- 🗺️ Pieces Mapped: {result['pieces_mapped']}
- ⏱️ Total Time: {result['processing_time']:.2f}s
- 🔍 V6 Inference: {result['v6_inference_time']:.1f}ms

**Architecture Used:**
- V6 Board Detection (256x256)
- Perspective Correction (512x512)
- Dual YOLO Detection (416x416)
- Direct Grid Mapping
- FEN Generation

*This is your original generate_fen_v6_geometric.py script running on Hugging Face.*"""
    else:
        return f"❌ **Processing Failed:** {result['error']}"

# Load models at startup
logger.info("🚀 Loading models...")
v6_model = load_v6_model()
yolo_model = load_yolo_model()

if v6_model is None or yolo_model is None:
    logger.error("❌ Failed to load required models")
    # Create a demo interface that shows the error
    demo = gr.Interface(
        fn=lambda x: "❌ Models not loaded. Please ensure best.pt and best_mobile.onnx are uploaded to the space.",
        inputs=gr.Image(type="numpy"),
        outputs=gr.Textbox(label="Error"),
        title="♟️ Chess FEN Generator - Model Loading Error",
        description="Models are not properly loaded. Please check the space configuration."
    )
else:
    logger.info("✅ All models loaded successfully")

    # Create Gradio interface
    demo = gr.Interface(
        fn=gradio_interface,
        inputs=gr.Image(type="numpy"),
        outputs=gr.Textbox(label="FEN Result"),
        title="♟️ Chess FEN Generator (Original Script)",
        description="Upload a chess board image to generate FEN notation using your original generate_fen_v6_geometric.py script.",
        examples=None
    )

if __name__ == "__main__":
    demo.launch()
