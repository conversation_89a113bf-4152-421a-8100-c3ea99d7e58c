"""
Convert Ultimate V2 Breakthrough Model to ONNX
Converts the latest V2 distilled model to ONNX format for production deployment.
"""

import torch
import torch.onnx
import numpy as np
import os
import time
from pathlib import Path

# Import the model architecture
from ultimate_breakthrough_v2 import UltimateBreakthroughV2

def load_v2_model(model_path, device):
    """Load the Ultimate V2 Breakthrough model"""
    print("🚀 Loading Ultimate V2 Breakthrough Model...")
    
    model = UltimateBreakthroughV2()
    model.load_state_dict(torch.load(model_path, map_location=device, weights_only=True))
    model.eval()
    model.to(device)
    
    params = sum(p.numel() for p in model.parameters())
    size_mb = sum(p.numel() * p.element_size() for p in model.parameters()) / (1024 * 1024)
    
    print(f"✅ Ultimate V2 loaded: {params:,} parameters, {size_mb:.2f}MB")
    return model

def convert_to_onnx(model, output_path, device, input_size=(1, 3, 256, 256)):
    """Convert PyTorch model to ONNX"""
    print(f"🔄 Converting to ONNX: {output_path}")
    
    # Create dummy input
    dummy_input = torch.randn(input_size, dtype=torch.float32, device=device)
    
    # Export to ONNX
    try:
        torch.onnx.export(
            model,                          # Model to export
            dummy_input,                    # Model input
            output_path,                    # Output path
            export_params=True,             # Store trained parameter weights
            opset_version=11,               # ONNX version
            do_constant_folding=True,       # Optimize constant folding
            input_names=['input'],          # Input names
            output_names=['output'],        # Output names
            dynamic_axes={                  # Dynamic axes for variable input sizes
                'input': {0: 'batch_size'},
                'output': {0: 'batch_size'}
            }
        )
        
        # Check file size
        onnx_size = os.path.getsize(output_path) / (1024 * 1024)
        print(f"✅ ONNX conversion successful!")
        print(f"📁 ONNX file size: {onnx_size:.2f}MB")
        
        return True, onnx_size
        
    except Exception as e:
        print(f"❌ ONNX conversion failed: {e}")
        return False, 0

def test_onnx_model(onnx_path, pytorch_model, device, num_tests=5):
    """Test ONNX model against PyTorch model"""
    print(f"🧪 Testing ONNX model: {onnx_path}")
    
    try:
        import onnxruntime as ort
        
        # Create ONNX runtime session
        ort_session = ort.InferenceSession(onnx_path)
        
        # Test with random inputs
        pytorch_times = []
        onnx_times = []
        max_diff = 0
        
        for i in range(num_tests):
            # Create test input
            test_input = torch.randn(1, 3, 256, 256, dtype=torch.float32)
            
            # PyTorch inference
            pytorch_model.eval()
            with torch.no_grad():
                start_time = time.perf_counter()
                pytorch_output = pytorch_model(test_input.to(device))
                pytorch_time = (time.perf_counter() - start_time) * 1000
                pytorch_times.append(pytorch_time)
            
            # ONNX inference
            ort_inputs = {ort_session.get_inputs()[0].name: test_input.numpy()}
            start_time = time.perf_counter()
            ort_outputs = ort_session.run(None, ort_inputs)
            onnx_time = (time.perf_counter() - start_time) * 1000
            onnx_times.append(onnx_time)
            
            # Compare outputs
            pytorch_np = pytorch_output.cpu().numpy()
            onnx_np = ort_outputs[0]
            diff = np.abs(pytorch_np - onnx_np).max()
            max_diff = max(max_diff, diff)
        
        # Calculate averages
        avg_pytorch_time = np.mean(pytorch_times)
        avg_onnx_time = np.mean(onnx_times)
        speedup = avg_pytorch_time / avg_onnx_time
        
        print(f"📊 ONNX Test Results:")
        print(f"   PyTorch Time: {avg_pytorch_time:.2f}ms")
        print(f"   ONNX Time: {avg_onnx_time:.2f}ms")
        print(f"   Speedup: {speedup:.2f}x")
        print(f"   Max Difference: {max_diff:.6f}")
        
        if max_diff < 1e-5:
            print("✅ ONNX model matches PyTorch model (excellent)")
        elif max_diff < 1e-4:
            print("✅ ONNX model matches PyTorch model (good)")
        elif max_diff < 1e-3:
            print("⚠️ ONNX model has small differences from PyTorch")
        else:
            print("❌ ONNX model has significant differences from PyTorch")
        
        return True, avg_onnx_time, max_diff
        
    except ImportError:
        print("⚠️ ONNX Runtime not installed. Install with: pip install onnxruntime")
        return False, 0, 0
    except Exception as e:
        print(f"❌ ONNX testing failed: {e}")
        return False, 0, 0

def main():
    """Main conversion function"""
    # Configuration
    model_path = "ultimate_v2_breakthrough/ultimate_v2_breakthrough.pth"
    output_dir = "ultimate_v2_breakthrough"
    onnx_filename = "ultimate_v2_breakthrough.onnx"
    
    # Check if model exists
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        return
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    onnx_path = os.path.join(output_dir, onnx_filename)
    
    # Setup device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Using device: {device}")
    print()
    
    try:
        # Load model
        model = load_v2_model(model_path, device)
        
        # Convert to ONNX
        success, onnx_size = convert_to_onnx(model, onnx_path, device)
        
        if success:
            # Test ONNX model
            test_success, onnx_time, max_diff = test_onnx_model(onnx_path, model, device)
            
            print("\n" + "="*60)
            print("🏆 ULTIMATE V2 ONNX CONVERSION SUMMARY")
            print("="*60)
            print(f"📁 Original Model: {model_path}")
            print(f"📁 ONNX Model: {onnx_path}")
            print(f"📊 ONNX Size: {onnx_size:.2f}MB")
            
            if test_success:
                print(f"⚡ ONNX Performance: {onnx_time:.2f}ms")
                print(f"🎯 Accuracy: Max diff {max_diff:.6f}")
            
            print("✅ Conversion completed successfully!")
            print("\n🚀 Next Steps:")
            print("1. Test ONNX model in your application")
            print("2. Deploy to production environment")
            print("3. Enjoy the optimized performance!")
            print("="*60)
        
    except Exception as e:
        print(f"❌ Conversion failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
