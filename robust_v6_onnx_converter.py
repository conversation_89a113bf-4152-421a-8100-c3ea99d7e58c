"""
Robust V6 to ONNX Converter
Handles the complex V6 model architecture with proper ONNX compatibility
"""

import torch
import torch.nn as nn
import numpy as np
import os
import time
from pathlib import Path

# Import the V6 model architecture
from breakthrough_unet_v6_simple import BreakthroughUNetV6, get_breakthrough_v6_model

class ONNXCompatibleV6(nn.Module):
    """ONNX-compatible version of V6 model"""
    
    def __init__(self, original_model):
        super(ONNXCompatibleV6, self).__init__()
        
        # Copy all the layers from original model
        self.inc = original_model.inc
        self.down1 = original_model.down1
        self.down2 = original_model.down2
        self.down3 = original_model.down3
        self.down4 = original_model.down4
        self.up1 = original_model.up1
        self.up2 = original_model.up2
        self.up3 = original_model.up3
        self.up4 = original_model.up4
        self.outc = original_model.outc
        
    def forward(self, x):
        # Encoder - same as original
        x1 = self.inc(x)
        x2 = self.down1(x1)
        x3 = self.down2(x2)
        x4 = self.down3(x3)
        x5 = self.down4(x4)
        
        # Decoder with ONNX-compatible operations
        x = self._onnx_compatible_up(self.up1, x5, x4)
        x = self._onnx_compatible_up(self.up2, x, x3)
        x = self._onnx_compatible_up(self.up3, x, x2)
        x = self._onnx_compatible_up(self.up4, x, x1)
        
        # Output
        logits = self.outc(x)
        return logits
    
    def _onnx_compatible_up(self, up_layer, x1, x2):
        """ONNX-compatible upsampling operation"""
        # Upsample x1
        x1_up = up_layer.up(x1)
        
        # Handle size mismatch with explicit operations
        h1, w1 = x1_up.shape[2], x1_up.shape[3]
        h2, w2 = x2.shape[2], x2.shape[3]
        
        # Calculate padding
        diff_h = h2 - h1
        diff_w = w2 - w1
        
        # Apply padding if needed
        if diff_h > 0 or diff_w > 0:
            pad_h_before = diff_h // 2
            pad_h_after = diff_h - pad_h_before
            pad_w_before = diff_w // 2
            pad_w_after = diff_w - pad_w_before
            
            x1_up = torch.nn.functional.pad(x1_up, [pad_w_before, pad_w_after, pad_h_before, pad_h_after])
        
        # Concatenate
        x = torch.cat([x2, x1_up], dim=1)
        
        # Apply convolution
        return up_layer.conv(x)

def load_v6_model(model_path, device):
    """Load the V6 model from checkpoint"""
    print(f"🔬 Loading V6 model from: {model_path}")
    
    # Create model
    model = get_breakthrough_v6_model(base_channels=32)
    
    # Load weights
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    
    # Handle different checkpoint formats
    if isinstance(checkpoint, dict):
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        elif 'state_dict' in checkpoint:
            model.load_state_dict(checkpoint['state_dict'])
        else:
            model.load_state_dict(checkpoint)
    else:
        model.load_state_dict(checkpoint)
    
    model.eval()
    model.to(device)
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    print(f"✅ V6 model loaded: {total_params:,} parameters")
    
    return model

def create_onnx_compatible_model(original_model):
    """Create ONNX-compatible version of the model"""
    print("🔧 Creating ONNX-compatible model...")
    
    onnx_model = ONNXCompatibleV6(original_model)
    onnx_model.eval()
    
    print("✅ ONNX-compatible model created")
    return onnx_model

def convert_to_onnx_simple(model, output_path, device):
    """Simple ONNX conversion with basic optimizations"""
    print(f"🔄 Converting to ONNX: {output_path}")
    
    model.eval()
    
    # Create dummy input
    dummy_input = torch.randn(1, 3, 256, 256).to(device)
    
    # Test model first
    with torch.no_grad():
        test_output = model(dummy_input)
        print(f"✅ Model test successful, output shape: {test_output.shape}")
    
    # Export with simpler settings for compatibility
    torch.onnx.export(
        model,
        dummy_input,
        output_path,
        export_params=True,
        opset_version=11,  # Use older, more stable opset
        do_constant_folding=True,
        input_names=['input'],
        output_names=['output'],
        verbose=False
    )
    
    print(f"✅ ONNX model saved: {output_path}")
    return output_path

def test_onnx_inference(onnx_path):
    """Simple ONNX inference test"""
    print(f"🧪 Testing ONNX inference: {onnx_path}")
    
    try:
        import onnxruntime as ort
        
        # Create simple session
        session = ort.InferenceSession(onnx_path, providers=['CPUExecutionProvider'])
        
        # Test inference
        input_name = session.get_inputs()[0].name
        test_input = np.random.randn(1, 3, 256, 256).astype(np.float32)
        
        # Single inference test
        output = session.run(None, {input_name: test_input})
        print(f"✅ ONNX inference successful, output shape: {output[0].shape}")
        
        # Quick benchmark (10 runs)
        times = []
        for _ in range(10):
            start_time = time.time()
            _ = session.run(None, {input_name: test_input})
            end_time = time.time()
            times.append((end_time - start_time) * 1000)
        
        avg_time = np.mean(times)
        print(f"✅ ONNX average inference time: {avg_time:.2f} ms (10 runs)")
        
        return True, avg_time
        
    except Exception as e:
        print(f"❌ ONNX test failed: {e}")
        return False, 0

def benchmark_pytorch_model(model, device):
    """Benchmark PyTorch model"""
    print("📊 Benchmarking PyTorch model...")
    
    model.eval()
    test_input = torch.randn(1, 3, 256, 256).to(device)
    
    times = []
    with torch.no_grad():
        for _ in range(10):
            start_time = time.time()
            _ = model(test_input)
            end_time = time.time()
            times.append((end_time - start_time) * 1000)
    
    avg_time = np.mean(times)
    print(f"✅ PyTorch average inference time: {avg_time:.2f} ms (10 runs)")
    
    return avg_time

def convert_v6_robust(model_path="best_model.pth", output_dir="optimized_models"):
    """Robust V6 to ONNX conversion"""
    print("🚀 Starting Robust V6 to ONNX Conversion")
    print("=" * 50)
    
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Using device: {device}")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # Step 1: Load original model
        original_model = load_v6_model(model_path, device)
        
        # Step 2: Create ONNX-compatible model
        onnx_model = create_onnx_compatible_model(original_model)
        
        # Step 3: Convert to ONNX
        onnx_path = os.path.join(output_dir, "v6_robust.onnx")
        convert_to_onnx_simple(onnx_model, onnx_path, device)
        
        # Step 4: Test ONNX model
        onnx_success, onnx_time = test_onnx_inference(onnx_path)
        
        # Step 5: Benchmark original model
        pytorch_time = benchmark_pytorch_model(original_model, device)
        
        # Step 6: Calculate results
        if onnx_success and onnx_time > 0:
            speedup = pytorch_time / onnx_time
        else:
            speedup = 0
        
        # Step 7: File sizes
        original_size = os.path.getsize(model_path) / (1024 * 1024)  # MB
        onnx_size = os.path.getsize(onnx_path) / (1024 * 1024)  # MB
        
        print("\n📊 CONVERSION RESULTS:")
        print("=" * 40)
        print(f"Original PyTorch model: {original_size:.2f} MB")
        print(f"ONNX model: {onnx_size:.2f} MB")
        print(f"Size change: {((onnx_size/original_size - 1)*100):+.1f}%")
        print(f"PyTorch inference: {pytorch_time:.2f} ms")
        print(f"ONNX inference: {onnx_time:.2f} ms")
        print(f"Performance: {speedup:.2f}x {'faster' if speedup > 1 else 'slower'}")
        
        return {
            'success': onnx_success,
            'onnx_path': onnx_path,
            'speedup': speedup,
            'pytorch_time': pytorch_time,
            'onnx_time': onnx_time,
            'original_size_mb': original_size,
            'onnx_size_mb': onnx_size
        }
        
    except Exception as e:
        print(f"❌ Conversion failed: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    # Run robust conversion
    results = convert_v6_robust(
        model_path="best_model.pth",
        output_dir="optimized_models"
    )
    
    print("\n🎉 Robust V6 Conversion Results:")
    print("=" * 50)
    for key, value in results.items():
        if isinstance(value, float):
            print(f"{key}: {value:.2f}")
        else:
            print(f"{key}: {value}")
    
    if results.get('success'):
        print("\n🚀 Next steps:")
        print("1. Test ONNX model with real chess board images")
        print("2. Upload to HuggingFace if performance is good")
        print("3. Update app.py to use ONNX V6 model")
    else:
        print("\n❌ Conversion failed. Check the error above.")
