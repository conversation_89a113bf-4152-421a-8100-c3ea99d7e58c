"""
Enhanced Knowledge Distillation with V6-Style Augmentation
Uses the same augmentation strategy as your original V6 training for better distillation
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import cv2
import os
import time
import random
import albumentations as A
from pathlib import Path
from torch.utils.data import Dataset, DataLoader
from PIL import Image

# Import the distillation components
from v6_knowledge_distillation import (
    LightweightStudentV6, 
    DistillationLoss,
    load_teacher_model,
    create_student_model,
    evaluate_distillation_quality,
    benchmark_models,
    convert_student_to_onnx,
    test_student_onnx
)

class V6StyleAugmentedDataset(Dataset):
    """Dataset with V6-style heavy augmentation for better distillation"""
    
    def __init__(self, dataset_path="segmentation_dataset", split="train", image_size=256, augment_factor=50):
        self.dataset_path = Path(dataset_path)
        self.split = split
        self.image_size = image_size
        self.augment_factor = augment_factor  # Generate 50x more samples through augmentation
        
        # Get image and label paths
        self.image_dir = self.dataset_path / "images" / split
        self.label_dir = self.dataset_path / "labels" / split
        
        # Get all image files
        self.image_files = list(self.image_dir.glob("*.jpg")) + list(self.image_dir.glob("*.png"))
        self.image_files.sort()
        
        # Create V6-style heavy augmentation pipeline
        self.augmentation = A.Compose([
            # Geometric transformations
            A.Rotate(limit=45, p=0.8),
            A.HorizontalFlip(p=0.5),
            A.VerticalFlip(p=0.3),
            A.ShiftScaleRotate(
                shift_limit=0.2, 
                scale_limit=0.3, 
                rotate_limit=45, 
                p=0.8
            ),
            A.Perspective(scale=(0.05, 0.15), p=0.6),
            A.ElasticTransform(alpha=1, sigma=50, alpha_affine=50, p=0.3),
            
            # Color and lighting augmentations
            A.RandomBrightnessContrast(
                brightness_limit=0.3, 
                contrast_limit=0.3, 
                p=0.8
            ),
            A.HueSaturationValue(
                hue_shift_limit=20, 
                sat_shift_limit=30, 
                val_shift_limit=20, 
                p=0.7
            ),
            A.CLAHE(clip_limit=2.0, tile_grid_size=(8, 8), p=0.5),
            A.RandomGamma(gamma_limit=(80, 120), p=0.5),
            
            # Noise and blur
            A.GaussNoise(var_limit=(10.0, 50.0), p=0.4),
            A.GaussianBlur(blur_limit=(3, 7), p=0.3),
            A.MotionBlur(blur_limit=7, p=0.3),
            
            # Weather effects
            A.RandomShadow(p=0.3),
            A.RandomFog(fog_coef_lower=0.1, fog_coef_upper=0.3, p=0.2),
            
            # Quality degradation
            A.ImageCompression(quality_lower=60, quality_upper=100, p=0.3),
            A.Downscale(scale_min=0.7, scale_max=0.9, p=0.2),
            
            # Final resize
            A.Resize(image_size, image_size, always_apply=True)
        ])
        
        print(f"📁 Loaded {len(self.image_files)} {split} images")
        print(f"🔄 Will generate {len(self.image_files) * augment_factor} augmented samples")
        
    def __len__(self):
        return len(self.image_files) * self.augment_factor
    
    def __getitem__(self, idx):
        # Get base image index and augmentation index
        base_idx = idx % len(self.image_files)
        aug_idx = idx // len(self.image_files)
        
        # Load base image
        image_path = self.image_files[base_idx]
        image = cv2.imread(str(image_path))
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Create synthetic mask (since we're doing knowledge distillation)
        # The teacher will provide the real supervision
        mask = self._create_synthetic_mask(image)
        
        # Apply V6-style heavy augmentation
        augmented = self.augmentation(image=image, mask=mask)
        aug_image = augmented['image']
        aug_mask = augmented['mask']
        
        # Convert to tensor and normalize
        image_tensor = torch.from_numpy(aug_image).permute(2, 0, 1).float() / 255.0
        
        return image_tensor
    
    def _create_synthetic_mask(self, image):
        """Create a synthetic mask for augmentation (teacher will provide real supervision)"""
        h, w = image.shape[:2]
        
        # Create a rough chess board region mask
        mask = np.zeros((h, w), dtype=np.uint8)
        
        # Find potential board region (simplified)
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        
        # Use edge detection to find rectangular regions
        edges = cv2.Canny(gray, 50, 150)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if contours:
            # Find largest rectangular contour
            largest_contour = max(contours, key=cv2.contourArea)
            
            # Approximate to rectangle
            epsilon = 0.02 * cv2.arcLength(largest_contour, True)
            approx = cv2.approxPolyDP(largest_contour, epsilon, True)
            
            if len(approx) >= 4:
                cv2.fillPoly(mask, [approx], 255)
            else:
                # Fallback: use bounding rectangle
                x, y, w, h = cv2.boundingRect(largest_contour)
                mask[y:y+h, x:x+w] = 255
        else:
            # Fallback: center region
            h_start, h_end = h//4, 3*h//4
            w_start, w_end = w//4, 3*w//4
            mask[h_start:h_end, w_start:w_end] = 255
        
        return mask.astype(np.float32) / 255.0

def distill_with_v6_augmentation(teacher, student, device, dataset_path="segmentation_dataset", 
                                num_epochs=100, batch_size=4, lr=0.001):
    """Perform knowledge distillation with V6-style heavy augmentation"""
    print("🧠 Starting knowledge distillation with V6-STYLE HEAVY AUGMENTATION...")
    print(f"📚 Training for {num_epochs} epochs with batch size {batch_size}")
    print("🔄 Using 50x augmentation factor for massive data diversity")
    
    # Create heavily augmented datasets
    train_dataset = V6StyleAugmentedDataset(dataset_path, "train", image_size=256, augment_factor=50)
    val_dataset = V6StyleAugmentedDataset(dataset_path, "val", image_size=256, augment_factor=10)
    
    # Create dataloaders
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    # Setup training with V6-style configuration
    optimizer = optim.AdamW(
        student.parameters(), 
        lr=lr, 
        weight_decay=1e-4,
        betas=(0.9, 0.999),
        eps=1e-8
    )
    
    # V6-style OneCycleLR scheduler
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=lr,
        epochs=num_epochs,
        steps_per_epoch=len(train_loader),
        pct_start=0.1,
        anneal_strategy='cos'
    )
    
    # V6-style advanced loss
    criterion = DistillationLoss(temperature=4.0, alpha=0.7, beta=0.2)
    
    teacher.eval()
    student.train()
    
    best_loss = float('inf')
    
    for epoch in range(num_epochs):
        # Training phase
        student.train()
        epoch_loss = 0
        num_batches = 0
        
        for batch_idx, images in enumerate(train_loader):
            images = images.to(device)
            
            # Teacher forward pass (no gradients)
            with torch.no_grad():
                teacher_output = teacher(images)
            
            # Student forward pass
            student_output = student(images)
            
            # Calculate distillation loss
            total_loss, dist_loss, output_loss, feat_loss = criterion(
                student_output, teacher_output
            )
            
            # Backward pass
            optimizer.zero_grad()
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(student.parameters(), max_norm=1.0)
            optimizer.step()
            scheduler.step()  # V6-style per-step scheduling
            
            epoch_loss += total_loss.item()
            num_batches += 1
            
            # Progress logging
            if batch_idx % 50 == 0:
                current_lr = optimizer.param_groups[0]['lr']
                print(f"  Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}, "
                      f"Loss: {total_loss.item():.4f}, LR: {current_lr:.6f}")
        
        # Validation phase
        student.eval()
        val_loss = 0
        val_batches = 0
        
        with torch.no_grad():
            for images in val_loader:
                images = images.to(device)
                teacher_output = teacher(images)
                student_output = student(images)
                
                total_loss, _, _, _ = criterion(student_output, teacher_output)
                val_loss += total_loss.item()
                val_batches += 1
        
        avg_train_loss = epoch_loss / num_batches
        avg_val_loss = val_loss / val_batches if val_batches > 0 else 0
        current_lr = optimizer.param_groups[0]['lr']
        
        # Save best model
        if avg_val_loss < best_loss:
            best_loss = avg_val_loss
            torch.save(student.state_dict(), 'best_v6_augmented_student.pth')
        
        # Epoch summary
        print(f"Epoch {epoch+1}/{num_epochs}: "
              f"Train Loss: {avg_train_loss:.4f}, "
              f"Val Loss: {avg_val_loss:.4f}, "
              f"Best: {best_loss:.4f}, "
              f"LR: {current_lr:.6f}")
    
    print("✅ V6-style augmented knowledge distillation completed!")
    
    # Load best model
    student.load_state_dict(torch.load('best_v6_augmented_student.pth'))
    
    return student

def run_v6_style_distillation(model_path="best_model.pth", 
                             dataset_path="segmentation_dataset",
                             output_dir="v6_style_distilled"):
    """Run complete distillation pipeline with V6-style augmentation"""
    print("🚀 V6-STYLE KNOWLEDGE DISTILLATION PIPELINE")
    print("=" * 60)
    print(f"📁 Using dataset: {dataset_path}")
    print("🔄 V6-Style Heavy Augmentation: 50x train, 10x val")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Using device: {device}")
    
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # Step 1: Load teacher model
        teacher = load_teacher_model(model_path, device)
        
        # Step 2: Create student model
        student = create_student_model(device, base_channels=8)
        
        # Step 3: Perform V6-style distillation
        distilled_student = distill_with_v6_augmentation(
            teacher, student, device, dataset_path,
            num_epochs=100, batch_size=4, lr=0.001
        )
        
        # Step 4: Evaluate distillation quality
        mse, mae, max_diff = evaluate_distillation_quality(teacher, distilled_student, device)
        
        # Step 5: Benchmark performance
        teacher_time, student_time, speedup = benchmark_models(teacher, distilled_student, device)
        
        # Step 6: Save student model
        student_path = os.path.join(output_dir, "v6_style_distilled_student.pth")
        torch.save(distilled_student.state_dict(), student_path)
        
        # Step 7: Convert to ONNX
        onnx_path = os.path.join(output_dir, "v6_style_distilled_student.onnx")
        convert_student_to_onnx(distilled_student, onnx_path, device)
        
        # Step 8: Test ONNX
        onnx_success, onnx_time = test_student_onnx(onnx_path, distilled_student, device)
        
        # Step 9: Calculate results
        teacher_params = sum(p.numel() for p in teacher.parameters())
        student_params = sum(p.numel() for p in distilled_student.parameters())
        
        original_size = os.path.getsize(model_path) / (1024 * 1024)
        student_size = os.path.getsize(student_path) / (1024 * 1024)
        onnx_size = os.path.getsize(onnx_path) / (1024 * 1024)
        
        print("\n🎉 V6-STYLE DISTILLATION RESULTS:")
        print("=" * 50)
        print(f"✅ V6-Style Heavy Augmentation: 50x data diversity")
        print(f"Teacher V6 parameters: {teacher_params:,}")
        print(f"Student V6 parameters: {student_params:,}")
        print(f"Parameter reduction: {(1 - student_params/teacher_params)*100:.1f}%")
        print()
        print(f"Teacher inference: {teacher_time:.2f} ms")
        print(f"Student PyTorch: {student_time:.2f} ms")
        print(f"Student ONNX: {onnx_time:.2f} ms")
        print(f"PyTorch speedup: {speedup:.2f}x")
        print(f"ONNX speedup: {teacher_time/onnx_time:.2f}x")
        print()
        print(f"Knowledge transfer MSE: {mse:.6f}")
        print(f"Knowledge transfer MAE: {mae:.6f}")
        print(f"Max difference: {max_diff:.6f}")
        print()
        print(f"Original model size: {original_size:.2f} MB")
        print(f"Student PyTorch size: {student_size:.2f} MB")
        print(f"Student ONNX size: {onnx_size:.2f} MB")
        print(f"Size reduction: {(1 - student_size/original_size)*100:.1f}%")
        
        return {
            'teacher_params': teacher_params,
            'student_params': student_params,
            'parameter_reduction': (1 - student_params/teacher_params)*100,
            'teacher_time': teacher_time,
            'student_time': student_time,
            'onnx_time': onnx_time,
            'pytorch_speedup': speedup,
            'onnx_speedup': teacher_time/onnx_time,
            'knowledge_mse': mse,
            'knowledge_mae': mae,
            'max_diff': max_diff,
            'student_path': student_path,
            'onnx_path': onnx_path,
            'size_reduction': (1 - student_size/original_size)*100
        }
        
    except Exception as e:
        print(f"❌ V6-style distillation failed: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    # Run V6-style distillation
    results = run_v6_style_distillation(
        model_path="best_model.pth",
        dataset_path="segmentation_dataset",
        output_dir="v6_style_distilled"
    )
    
    print("\n🎓 V6-STYLE DISTILLATION SUMMARY:")
    print("=" * 50)
    for key, value in results.items():
        if isinstance(value, float):
            print(f"{key}: {value:.4f}")
        else:
            print(f"{key}: {value}")
    
    print("\n🚀 Next Steps:")
    print("1. Test V6-style distilled model on real chess boards")
    print("2. Compare with original distillation results")
    print("3. Upload best performing model to HuggingFace")
    print("4. Enjoy massive speedup with V6-level accuracy!")
