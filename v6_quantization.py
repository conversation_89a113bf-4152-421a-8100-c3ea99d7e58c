"""
V6 Model Quantization
Apply INT8 quantization to your exact V6 model for speedup while preserving architecture
"""

import torch
import torch.nn as nn
import numpy as np
import os
import time
from pathlib import Path

# Import your exact V6 model architecture
from breakthrough_unet_v6_simple import BreakthroughUNetV6, get_breakthrough_v6_model

def load_v6_model(model_path, device):
    """Load your exact V6 model"""
    print(f"🔬 Loading your exact V6 model from: {model_path}")
    
    model = get_breakthrough_v6_model(base_channels=32)
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    
    if isinstance(checkpoint, dict):
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        elif 'state_dict' in checkpoint:
            model.load_state_dict(checkpoint['state_dict'])
        else:
            model.load_state_dict(checkpoint)
    else:
        model.load_state_dict(checkpoint)
    
    model.eval()
    model.to(device)
    
    total_params = sum(p.numel() for p in model.parameters())
    print(f"✅ Your exact V6 model loaded: {total_params:,} parameters")
    
    return model

def apply_dynamic_quantization(model):
    """Apply dynamic INT8 quantization to your model"""
    print("🔢 Applying dynamic INT8 quantization to your exact V6 model...")
    
    # Apply dynamic quantization to linear and conv layers
    quantized_model = torch.quantization.quantize_dynamic(
        model,
        {nn.Conv2d, nn.Linear, nn.BatchNorm2d},
        dtype=torch.qint8
    )
    
    print("✅ Dynamic quantization applied to your exact model")
    return quantized_model

def benchmark_model(model, device, model_name, num_runs=20):
    """Benchmark model performance"""
    print(f"📊 Benchmarking {model_name}...")
    
    model.eval()
    test_input = torch.randn(1, 3, 256, 256).to(device)
    
    # Warmup
    with torch.no_grad():
        for _ in range(5):
            _ = model(test_input)
    
    # Benchmark
    times = []
    with torch.no_grad():
        for _ in range(num_runs):
            start_time = time.time()
            _ = model(test_input)
            end_time = time.time()
            times.append((end_time - start_time) * 1000)
    
    avg_time = np.mean(times)
    std_time = np.std(times)
    print(f"✅ {model_name}: {avg_time:.2f} ± {std_time:.2f} ms")
    
    return avg_time

def test_accuracy_preservation(original_model, quantized_model, device):
    """Test if quantization preserves accuracy"""
    print("🔍 Testing accuracy preservation after quantization...")
    
    test_cases = [
        torch.randn(1, 3, 256, 256).to(device),
        torch.ones(1, 3, 256, 256).to(device) * 0.5,
        torch.zeros(1, 3, 256, 256).to(device)
    ]
    
    max_diff = 0
    with torch.no_grad():
        for i, test_input in enumerate(test_cases):
            # Original model output
            original_output = original_model(test_input)
            
            # Quantized model output
            quantized_output = quantized_model(test_input)
            
            # Calculate difference
            diff = torch.abs(original_output - quantized_output).max().item()
            max_diff = max(max_diff, diff)
            print(f"  Test case {i+1}: Max difference = {diff:.6f}")
    
    print(f"✅ Maximum output difference: {max_diff:.6f}")
    
    if max_diff < 0.01:
        print("🎉 Excellent accuracy preservation!")
    elif max_diff < 0.1:
        print("✅ Good accuracy preservation")
    else:
        print("⚠️ Some accuracy loss detected")
    
    return max_diff

def quantize_v6_model(model_path="best_model.pth", output_dir="quantized_v6"):
    """Apply quantization to your exact V6 model"""
    print("🚀 Quantizing Your Exact V6 Model")
    print("🔧 NO ARCHITECTURAL CHANGES - Just INT8 quantization")
    print("=" * 60)
    
    device = torch.device('cpu')  # Quantization works on CPU
    print(f"🖥️ Using device: {device}")
    
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # Step 1: Load your exact model
        original_model = load_v6_model(model_path, device)
        
        # Step 2: Benchmark original model
        original_time = benchmark_model(original_model, device, "Your Original V6")
        
        # Step 3: Apply quantization
        quantized_model = apply_dynamic_quantization(original_model)
        
        # Step 4: Benchmark quantized model
        quantized_time = benchmark_model(quantized_model, device, "Your Quantized V6")
        
        # Step 5: Test accuracy preservation
        accuracy_diff = test_accuracy_preservation(original_model, quantized_model, device)
        
        # Step 6: Calculate speedup
        speedup = original_time / quantized_time if quantized_time > 0 else 0
        
        # Step 7: Save quantized model
        quantized_path = os.path.join(output_dir, "v6_quantized.pth")
        torch.save(quantized_model.state_dict(), quantized_path)
        
        # Step 8: File sizes
        original_size = os.path.getsize(model_path) / (1024 * 1024)
        quantized_size = os.path.getsize(quantized_path) / (1024 * 1024)
        
        print("\n📊 QUANTIZATION RESULTS:")
        print("=" * 40)
        print(f"✅ Architecture: UNCHANGED (your exact model)")
        print(f"✅ Quantization: INT8 dynamic quantization applied")
        print()
        print(f"Your Original V6: {original_time:.2f} ms")
        print(f"Your Quantized V6: {quantized_time:.2f} ms")
        print(f"Speedup: {speedup:.2f}x {'faster' if speedup > 1 else 'slower'}")
        print(f"Accuracy difference: {accuracy_diff:.6f}")
        print()
        print(f"Original size: {original_size:.2f} MB")
        print(f"Quantized size: {quantized_size:.2f} MB")
        print(f"Size reduction: {(1 - quantized_size/original_size)*100:.1f}%")
        
        return {
            'success': True,
            'quantized_path': quantized_path,
            'original_time': original_time,
            'quantized_time': quantized_time,
            'speedup': speedup,
            'accuracy_diff': accuracy_diff,
            'original_size_mb': original_size,
            'quantized_size_mb': quantized_size,
            'architecture_changed': False
        }
        
    except Exception as e:
        print(f"❌ Quantization failed: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    # Quantize your exact V6 model
    results = quantize_v6_model(
        model_path="best_model.pth",
        output_dir="quantized_v6"
    )
    
    print("\n🎉 Your V6 Quantization Results:")
    print("=" * 40)
    for key, value in results.items():
        if isinstance(value, float):
            print(f"{key}: {value:.4f}")
        else:
            print(f"{key}: {value}")
    
    if results.get('success'):
        print("\n✅ SUCCESS: Your exact V6 model quantized!")
        print("🔧 Architecture unchanged - just INT8 quantization")
        
        if results.get('speedup', 0) > 1:
            print(f"🚀 Speedup achieved: {results['speedup']:.2f}x faster!")
        else:
            print("📝 Note: Quantization may not speed up complex architectures")
    else:
        print("\n❌ Quantization failed")
