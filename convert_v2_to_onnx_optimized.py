"""
Optimized ONNX Conversion for Ultimate V2 Breakthrough Model
Creates a highly optimized ONNX version with better accuracy and performance.
"""

import torch
import torch.onnx
import numpy as np
import os
import time
from pathlib import Path

# Import the model architecture
from ultimate_breakthrough_v2 import UltimateBreakthroughV2

def load_v2_model(model_path, device):
    """Load the Ultimate V2 Breakthrough model"""
    print("🚀 Loading Ultimate V2 Breakthrough Model...")
    
    model = UltimateBreakthroughV2()
    model.load_state_dict(torch.load(model_path, map_location=device, weights_only=True))
    model.eval()
    model.to(device)
    
    # Set model to evaluation mode and disable gradients
    for param in model.parameters():
        param.requires_grad = False
    
    params = sum(p.numel() for p in model.parameters())
    size_mb = sum(p.numel() * p.element_size() for p in model.parameters()) / (1024 * 1024)
    
    print(f"✅ Ultimate V2 loaded: {params:,} parameters, {size_mb:.2f}MB")
    return model

def convert_to_optimized_onnx(model, output_path, device, input_size=(1, 3, 256, 256)):
    """Convert PyTorch model to optimized ONNX"""
    print(f"🔄 Converting to Optimized ONNX: {output_path}")
    
    # Create dummy input
    dummy_input = torch.randn(input_size, dtype=torch.float32, device=device)
    
    # Warm up the model
    with torch.no_grad():
        for _ in range(3):
            _ = model(dummy_input)
    
    # Export to ONNX with optimized settings
    try:
        torch.onnx.export(
            model,                          # Model to export
            dummy_input,                    # Model input
            output_path,                    # Output path
            export_params=True,             # Store trained parameter weights
            opset_version=14,               # Use newer ONNX version for better optimization
            do_constant_folding=True,       # Optimize constant folding
            input_names=['input'],          # Input names
            output_names=['output'],        # Output names
            dynamic_axes={                  # Dynamic axes for variable input sizes
                'input': {0: 'batch_size'},
                'output': {0: 'batch_size'}
            },
            training=torch.onnx.TrainingMode.EVAL,  # Explicitly set to eval mode
            verbose=False                   # Reduce verbosity
        )
        
        # Check file size
        onnx_size = os.path.getsize(output_path) / (1024 * 1024)
        print(f"✅ Optimized ONNX conversion successful!")
        print(f"📁 ONNX file size: {onnx_size:.2f}MB")
        
        return True, onnx_size
        
    except Exception as e:
        print(f"❌ ONNX conversion failed: {e}")
        import traceback
        traceback.print_exc()
        return False, 0

def test_onnx_accuracy(onnx_path, pytorch_model, device, num_tests=10):
    """Test ONNX model accuracy against PyTorch model"""
    print(f"🧪 Testing ONNX accuracy: {onnx_path}")
    
    try:
        import onnxruntime as ort
        
        # Create ONNX runtime session with optimizations
        providers = ['CUDAExecutionProvider', 'CPUExecutionProvider'] if torch.cuda.is_available() else ['CPUExecutionProvider']
        ort_session = ort.InferenceSession(onnx_path, providers=providers)
        
        # Test with multiple inputs
        differences = []
        pytorch_times = []
        onnx_times = []
        
        for i in range(num_tests):
            # Create test input
            test_input = torch.randn(1, 3, 256, 256, dtype=torch.float32)
            
            # PyTorch inference
            pytorch_model.eval()
            with torch.no_grad():
                start_time = time.perf_counter()
                pytorch_output = pytorch_model(test_input.to(device))
                pytorch_time = (time.perf_counter() - start_time) * 1000
                pytorch_times.append(pytorch_time)
            
            # ONNX inference
            ort_inputs = {ort_session.get_inputs()[0].name: test_input.numpy()}
            start_time = time.perf_counter()
            ort_outputs = ort_session.run(None, ort_inputs)
            onnx_time = (time.perf_counter() - start_time) * 1000
            onnx_times.append(onnx_time)
            
            # Compare outputs
            pytorch_np = pytorch_output.cpu().numpy()
            onnx_np = ort_outputs[0]
            
            # Calculate various difference metrics
            abs_diff = np.abs(pytorch_np - onnx_np)
            max_diff = abs_diff.max()
            mean_diff = abs_diff.mean()
            rel_diff = abs_diff / (np.abs(pytorch_np) + 1e-8)
            max_rel_diff = rel_diff.max()
            
            differences.append({
                'max_abs': max_diff,
                'mean_abs': mean_diff,
                'max_rel': max_rel_diff
            })
        
        # Calculate statistics
        avg_pytorch_time = np.mean(pytorch_times)
        avg_onnx_time = np.mean(onnx_times)
        speedup = avg_pytorch_time / avg_onnx_time
        
        max_abs_diff = max(d['max_abs'] for d in differences)
        mean_abs_diff = np.mean([d['mean_abs'] for d in differences])
        max_rel_diff = max(d['max_rel'] for d in differences)
        
        print(f"📊 ONNX Accuracy Test Results:")
        print(f"   PyTorch Time: {avg_pytorch_time:.2f}ms ± {np.std(pytorch_times):.2f}ms")
        print(f"   ONNX Time: {avg_onnx_time:.2f}ms ± {np.std(onnx_times):.2f}ms")
        print(f"   Speedup: {speedup:.2f}x")
        print(f"   Max Absolute Diff: {max_abs_diff:.6f}")
        print(f"   Mean Absolute Diff: {mean_abs_diff:.6f}")
        print(f"   Max Relative Diff: {max_rel_diff:.6f}")
        
        # Accuracy assessment
        if max_abs_diff < 1e-6:
            accuracy_status = "✅ Excellent (near-perfect match)"
        elif max_abs_diff < 1e-5:
            accuracy_status = "✅ Very Good"
        elif max_abs_diff < 1e-4:
            accuracy_status = "✅ Good"
        elif max_abs_diff < 1e-3:
            accuracy_status = "⚠️ Acceptable"
        else:
            accuracy_status = "❌ Poor (significant differences)"
        
        print(f"   Accuracy: {accuracy_status}")
        
        return True, avg_onnx_time, max_abs_diff, speedup
        
    except ImportError:
        print("⚠️ ONNX Runtime not installed. Install with: pip install onnxruntime-gpu")
        return False, 0, 0, 0
    except Exception as e:
        print(f"❌ ONNX testing failed: {e}")
        import traceback
        traceback.print_exc()
        return False, 0, 0, 0

def main():
    """Main optimized conversion function"""
    # Configuration
    model_path = "ultimate_v2_breakthrough/ultimate_v2_breakthrough.pth"
    output_dir = "ultimate_v2_breakthrough"
    onnx_filename = "ultimate_v2_breakthrough_optimized.onnx"
    
    # Check if model exists
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        return
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    onnx_path = os.path.join(output_dir, onnx_filename)
    
    # Setup device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Using device: {device}")
    print()
    
    try:
        # Load model
        model = load_v2_model(model_path, device)
        
        # Convert to optimized ONNX
        success, onnx_size = convert_to_optimized_onnx(model, onnx_path, device)
        
        if success:
            # Test ONNX model accuracy
            test_success, onnx_time, max_diff, speedup = test_onnx_accuracy(onnx_path, model, device)
            
            print("\n" + "="*70)
            print("🏆 ULTIMATE V2 OPTIMIZED ONNX CONVERSION SUMMARY")
            print("="*70)
            print(f"📁 Original Model: {model_path}")
            print(f"📁 Optimized ONNX: {onnx_path}")
            print(f"📊 ONNX Size: {onnx_size:.2f}MB")
            
            if test_success:
                print(f"⚡ ONNX Performance: {onnx_time:.2f}ms")
                print(f"🚀 Speedup: {speedup:.2f}x")
                print(f"🎯 Max Accuracy Diff: {max_diff:.6f}")
                
                if max_diff < 1e-4:
                    print("✅ EXCELLENT: High accuracy ONNX conversion!")
                elif max_diff < 1e-3:
                    print("✅ GOOD: Acceptable accuracy for production use")
                else:
                    print("⚠️ WARNING: Consider accuracy implications")
            
            print("\n🚀 Next Steps:")
            print("1. Test ONNX model in your chess application")
            print("2. Compare performance with original V6 model")
            print("3. Deploy to production for maximum efficiency!")
            print("="*70)
        
    except Exception as e:
        print(f"❌ Conversion failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
