"""
Hugging Face Spaces API for Chess FEN Generation
Clean API wrapper that calls generate_fen_v6_geometric.py script
"""

import os
import sys
import tempfile
import time
import json
import subprocess
from pathlib import Path
import cv2
import numpy as np
import gradio as gr
import logging
from PIL import Image

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add current directory to path so we can import the FEN generation script
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def process_chess_image_api(image_file):
    """
    Main API function that calls generate_fen_v6_geometric.py script
    """
    try:
        start_time = time.time()
        logger.info("🚀 Processing chess image via generate_fen_v6_geometric.py")
        
        # Save uploaded image to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as tmp_file:
            image_path = tmp_file.name
            
            # Convert and save image
            if isinstance(image_file, np.ndarray):
                # Convert numpy array to PIL Image then save
                pil_image = Image.fromarray(image_file)
                pil_image.save(image_path)
            elif hasattr(image_file, 'save'):
                # PIL Image
                image_file.save(image_path)
            else:
                # OpenCV format
                cv2.imwrite(image_path, image_file)
        
        logger.info(f"📁 Saved image to: {image_path}")
        
        # Call the original generate_fen_v6_geometric.py script
        logger.info("🔍 Calling generate_fen_v6_geometric.py script...")
        
        # Prepare command to run the script
        cmd = [
            sys.executable, 
            "generate_fen_v6_geometric.py",
            image_path,
            "--output", "temp_output.png",
            "--v6_model", "best.pt",
            "--piece_model", "best_mobile.onnx"
        ]
        
        # Run the script and capture output
        script_start = time.time()
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=120  # 2 minute timeout
        )
        script_time = time.time() - script_start
        
        # Clean up temporary image
        try:
            os.unlink(image_path)
        except:
            pass
        
        if result.returncode == 0:
            # Script succeeded
            logger.info("✅ generate_fen_v6_geometric.py completed successfully")
            
            # Try to read the generated FEN file
            fen_file = "temp_output.fen"
            fen = None
            
            if os.path.exists(fen_file):
                with open(fen_file, 'r') as f:
                    fen = f.read().strip()
                logger.info(f"📝 FEN read from file: {fen}")
                
                # Clean up FEN file
                try:
                    os.unlink(fen_file)
                except:
                    pass
            else:
                # Try to extract FEN from stdout
                output_lines = result.stdout.split('\n')
                for line in output_lines:
                    if 'FEN:' in line:
                        fen = line.split('FEN:')[-1].strip()
                        break
                
                if not fen:
                    # Look for FEN pattern in output
                    import re
                    fen_pattern = r'([rnbqkpRNBQKP1-8]+/){7}[rnbqkpRNBQKP1-8]+\s+[wb]\s+[KQkq-]+\s+[a-h3-6-]+\s+\d+\s+\d+'
                    match = re.search(fen_pattern, result.stdout)
                    if match:
                        fen = match.group(0)
            
            # Clean up output image if it exists
            try:
                os.unlink("temp_output.png")
            except:
                pass
            
            total_time = time.time() - start_time
            
            if fen:
                return {
                    "success": True,
                    "fen": fen,
                    "processing_time": total_time,
                    "script_time": script_time,
                    "stdout": result.stdout,
                    "method": "generate_fen_v6_geometric.py"
                }
            else:
                return {
                    "success": False,
                    "error": "FEN not found in script output",
                    "stdout": result.stdout,
                    "stderr": result.stderr
                }
        else:
            # Script failed
            logger.error(f"❌ generate_fen_v6_geometric.py failed with return code: {result.returncode}")
            logger.error(f"STDERR: {result.stderr}")
            
            return {
                "success": False,
                "error": f"Script failed with return code {result.returncode}",
                "stdout": result.stdout,
                "stderr": result.stderr
            }
            
    except subprocess.TimeoutExpired:
        logger.error("❌ Script execution timed out")
        return {
            "success": False,
            "error": "Script execution timed out (>2 minutes)",
            "fen": None
        }
        
    except Exception as e:
        logger.error(f"❌ API processing failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            "success": False,
            "error": str(e),
            "fen": None
        }

def gradio_interface(image):
    """Gradio interface function"""
    if image is None:
        return "Please upload an image."
    
    # Process the image
    result = process_chess_image_api(image)
    
    if result["success"]:
        return f"""✅ **Chess FEN Generated Successfully!**

**FEN Notation:** `{result['fen']}`

**Processing Details:**
- ⏱️ Total Time: {result['processing_time']:.2f}s
- 🔍 Script Time: {result['script_time']:.2f}s
- 🛠️ Method: {result['method']}

**Architecture Used:**
- V6 Board Detection (256x256)
- Perspective Correction (512x512)  
- Dual YOLO Detection (416x416)
- Direct Grid Mapping
- FEN Generation

*This calls your original generate_fen_v6_geometric.py script directly.*

**Script Output:**
```
{result.get('stdout', 'No output captured')[:500]}...
```"""
    else:
        return f"""❌ **Processing Failed:** {result['error']}

**Debug Information:**
- Error: {result.get('error', 'Unknown error')}

**Script Output (stdout):**
```
{result.get('stdout', 'No stdout')[:500]}...
```

**Script Errors (stderr):**
```
{result.get('stderr', 'No stderr')[:500]}...
```"""

# Create Gradio interface
demo = gr.Interface(
    fn=gradio_interface,
    inputs=gr.Image(type="numpy"),
    outputs=gr.Textbox(label="FEN Result", lines=20),
    title="♟️ Chess FEN Generator (Original Script Caller)",
    description="""Upload a chess board image to generate FEN notation. 

This API calls your original `generate_fen_v6_geometric.py` script directly as a subprocess.

**Requirements:**
- `generate_fen_v6_geometric.py` must be in the same directory
- `best.pt` (V6 model) must be available
- `best_mobile.onnx` (YOLO model) must be available
- All dependencies must be installed""",
    examples=None
)

if __name__ == "__main__":
    demo.launch()
