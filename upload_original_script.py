"""
Upload Original Script Version to Hugging Face
Simple script to upload the original generate_fen_v6_geometric.py based implementation
"""

import os
import shutil
from huggingface_hub import HfApi, upload_file

def upload_original_version():
    """Upload the original script version to Hugging Face"""
    
    # Hugging Face credentials
    repo_id = "yamero999/chess-fen-generation-api"
    
    try:
        # Initialize HF API
        api = HfApi()
        
        print("🚀 Uploading original script version to Hugging Face...")
        
        # Upload the complete implementation as app.py
        print("📤 Uploading app_complete.py as app.py...")
        upload_file(
            path_or_fileobj="app_complete.py",
            path_in_repo="app.py",
            repo_id=repo_id,
            repo_type="space",
            commit_message="🚀 COMPLETE IMPLEMENTATION: Full generate_fen_v6_geometric.py with V6 model architecture included"
        )
        
        print("✅ Original script version uploaded successfully!")
        print(f"🌐 URL: https://huggingface.co/spaces/{repo_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Upload failed: {e}")
        return False

if __name__ == "__main__":
    success = upload_original_version()
    if success:
        print("\n🎯 ORIGINAL SCRIPT DEPLOYMENT COMPLETE!")
        print("\nThis version:")
        print("✅ Uses your original generate_fen_v6_geometric.py architecture")
        print("✅ V6 Board Detection (256x256)")
        print("✅ Perspective Correction (512x512)")
        print("✅ Dual YOLO Detection (416x416)")
        print("✅ Direct Grid Mapping")
        print("✅ No V6 filtering")
        print("✅ Exact same processing flow")
        print("\n⚠️ Note: You'll need to add the V6 model architecture files to the HF space")
    else:
        print("\n❌ Deployment failed!")
