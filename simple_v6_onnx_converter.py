"""
Simple V6 to ONNX Converter
Converts the existing V6 model to optimized ONNX format without distillation
to preserve accuracy while gaining performance benefits.
"""

import torch
import torch.nn as nn
import numpy as np
import os
import time
from pathlib import Path

# Import the V6 model architecture
from breakthrough_unet_v6_simple import BreakthroughUNetV6, get_breakthrough_v6_model

def load_v6_model(model_path, device):
    """Load the V6 model from checkpoint"""
    print(f"🔬 Loading V6 model from: {model_path}")
    
    # Create model
    model = get_breakthrough_v6_model(base_channels=32)
    
    # Load weights
    checkpoint = torch.load(model_path, map_location=device)
    
    # Handle different checkpoint formats
    if isinstance(checkpoint, dict):
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        elif 'state_dict' in checkpoint:
            model.load_state_dict(checkpoint['state_dict'])
        else:
            model.load_state_dict(checkpoint)
    else:
        model.load_state_dict(checkpoint)
    
    model.eval()
    model.to(device)
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    print(f"✅ V6 model loaded: {total_params:,} parameters")
    
    return model

def convert_v6_to_onnx(model, output_path, device, input_size=(256, 256)):
    """Convert V6 model to optimized ONNX format"""
    print(f"🔄 Converting V6 to ONNX: {output_path}")
    
    model.eval()
    
    # Create dummy input
    dummy_input = torch.randn(1, 3, input_size[0], input_size[1]).to(device)
    
    # Test the model first
    print("🧪 Testing model before conversion...")
    with torch.no_grad():
        test_output = model(dummy_input)
        print(f"✅ Model test successful, output shape: {test_output.shape}")
    
    # Export to ONNX
    print("📤 Exporting to ONNX...")
    torch.onnx.export(
        model,
        dummy_input,
        output_path,
        export_params=True,
        opset_version=17,  # Latest stable opset
        do_constant_folding=True,  # Optimize constants
        input_names=['input'],
        output_names=['output'],
        dynamic_axes={
            'input': {0: 'batch_size'},
            'output': {0: 'batch_size'}
        },
        verbose=False
    )
    
    print(f"✅ ONNX model saved: {output_path}")
    return output_path

def optimize_onnx_model(onnx_path):
    """Optimize the ONNX model"""
    try:
        import onnx
        from onnxoptimizer import optimize
        
        print("🚀 Optimizing ONNX model...")
        
        # Load model
        onnx_model = onnx.load(onnx_path)
        
        # Apply optimizations
        optimized_model = optimize(onnx_model, passes=[
            'eliminate_deadend',
            'eliminate_identity',
            'eliminate_nop_dropout',
            'eliminate_nop_monotone_argmax',
            'eliminate_nop_pad',
            'eliminate_nop_transpose',
            'eliminate_unused_initializer',
            'extract_constant_to_initializer',
            'fuse_add_bias_into_conv',
            'fuse_bn_into_conv',
            'fuse_consecutive_concats',
            'fuse_consecutive_log_softmax',
            'fuse_consecutive_reduce_unsqueeze',
            'fuse_consecutive_squeezes',
            'fuse_consecutive_transposes',
            'fuse_matmul_add_bias_into_gemm',
            'fuse_pad_into_conv',
            'fuse_transpose_into_gemm',
        ])
        
        # Save optimized model
        optimized_path = onnx_path.replace('.onnx', '_optimized.onnx')
        onnx.save(optimized_model, optimized_path)
        
        print(f"✅ Optimized ONNX model saved: {optimized_path}")
        return optimized_path
        
    except ImportError:
        print("⚠️ onnxoptimizer not available, using basic ONNX export")
        return onnx_path
    except Exception as e:
        print(f"⚠️ ONNX optimization failed: {e}, using basic ONNX export")
        return onnx_path

def test_onnx_model(onnx_path, original_model, device):
    """Test the ONNX model and compare with original"""
    print(f"🧪 Testing ONNX model: {onnx_path}")
    
    try:
        import onnxruntime as ort
        
        # Create ONNX Runtime session with optimizations
        sess_options = ort.SessionOptions()
        sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
        sess_options.intra_op_num_threads = 1  # Optimal for vCPU
        sess_options.inter_op_num_threads = 1
        sess_options.execution_mode = ort.ExecutionMode.ORT_SEQUENTIAL
        sess_options.enable_mem_pattern = True
        sess_options.enable_cpu_mem_arena = True
        
        session = ort.InferenceSession(onnx_path, sess_options, providers=['CPUExecutionProvider'])
        
        # Test inference
        input_name = session.get_inputs()[0].name
        test_input = np.random.randn(1, 3, 256, 256).astype(np.float32)
        
        # ONNX inference
        onnx_output = session.run(None, {input_name: test_input})[0]
        
        # Original model inference
        with torch.no_grad():
            torch_input = torch.from_numpy(test_input).to(device)
            torch_output = original_model(torch_input).cpu().numpy()
        
        # Compare outputs
        diff = np.abs(onnx_output - torch_output).mean()
        print(f"✅ Output difference: {diff:.6f} (should be < 0.001)")
        
        # Benchmark ONNX
        print("📊 Benchmarking ONNX performance...")
        times = []
        for _ in range(100):
            start_time = time.time()
            _ = session.run(None, {input_name: test_input})
            end_time = time.time()
            times.append((end_time - start_time) * 1000)
        
        avg_time = np.mean(times)
        std_time = np.std(times)
        print(f"✅ ONNX inference: {avg_time:.2f} ± {std_time:.2f} ms")
        
        # Benchmark original model
        print("📊 Benchmarking original PyTorch performance...")
        torch_times = []
        with torch.no_grad():
            for _ in range(100):
                start_time = time.time()
                _ = original_model(torch_input)
                end_time = time.time()
                torch_times.append((end_time - start_time) * 1000)
        
        torch_avg_time = np.mean(torch_times)
        speedup = torch_avg_time / avg_time
        
        print(f"✅ PyTorch inference: {torch_avg_time:.2f} ms")
        print(f"🚀 ONNX Speedup: {speedup:.2f}x faster")
        
        return True, speedup
        
    except Exception as e:
        print(f"❌ ONNX model test failed: {e}")
        return False, 0

def apply_quantization_to_onnx(onnx_path):
    """Apply INT8 quantization to ONNX model"""
    try:
        from onnxruntime.quantization import quantize_dynamic, QuantType
        
        print("🔢 Applying INT8 quantization to ONNX model...")
        
        quantized_path = onnx_path.replace('.onnx', '_int8.onnx')
        
        quantize_dynamic(
            onnx_path,
            quantized_path,
            weight_type=QuantType.QInt8
        )
        
        print(f"✅ Quantized ONNX model saved: {quantized_path}")
        return quantized_path
        
    except ImportError:
        print("⚠️ ONNX quantization not available")
        return onnx_path
    except Exception as e:
        print(f"⚠️ ONNX quantization failed: {e}")
        return onnx_path

def convert_v6_model(model_path="best_model.pth", output_dir="optimized_models"):
    """Main function to convert V6 model to optimized ONNX"""
    print("🚀 Starting V6 to ONNX Conversion")
    print("=" * 50)
    
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Using device: {device}")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Step 1: Load V6 model
    model = load_v6_model(model_path, device)
    
    # Step 2: Convert to ONNX
    onnx_path = os.path.join(output_dir, "v6_model.onnx")
    convert_v6_to_onnx(model, onnx_path, device)
    
    # Step 3: Optimize ONNX
    optimized_path = optimize_onnx_model(onnx_path)
    
    # Step 4: Apply quantization
    quantized_path = apply_quantization_to_onnx(optimized_path)
    
    # Step 5: Test models
    print("\n🧪 Testing optimized ONNX model...")
    success, speedup = test_onnx_model(optimized_path, model, device)
    
    if quantized_path != optimized_path:
        print("\n🧪 Testing quantized ONNX model...")
        q_success, q_speedup = test_onnx_model(quantized_path, model, device)
    else:
        q_success, q_speedup = success, speedup
    
    # Step 6: File size comparison
    original_size = os.path.getsize(model_path) / (1024 * 1024)  # MB
    onnx_size = os.path.getsize(optimized_path) / (1024 * 1024)  # MB
    
    if quantized_path != optimized_path:
        quantized_size = os.path.getsize(quantized_path) / (1024 * 1024)  # MB
    else:
        quantized_size = onnx_size
    
    print("\n📊 CONVERSION RESULTS:")
    print("=" * 40)
    print(f"Original PyTorch model: {original_size:.2f} MB")
    print(f"Optimized ONNX model: {onnx_size:.2f} MB")
    print(f"Quantized ONNX model: {quantized_size:.2f} MB")
    print(f"Size reduction (optimized): {(1 - onnx_size/original_size)*100:.1f}%")
    print(f"Size reduction (quantized): {(1 - quantized_size/original_size)*100:.1f}%")
    print(f"Performance speedup (optimized): {speedup:.2f}x")
    print(f"Performance speedup (quantized): {q_speedup:.2f}x")
    
    return {
        'original_model_path': model_path,
        'onnx_model_path': optimized_path,
        'quantized_model_path': quantized_path,
        'speedup': speedup,
        'quantized_speedup': q_speedup,
        'size_reduction': (1 - onnx_size/original_size)*100,
        'quantized_size_reduction': (1 - quantized_size/original_size)*100
    }

if __name__ == "__main__":
    # Run conversion
    results = convert_v6_model(
        model_path="best_model.pth",
        output_dir="optimized_models"
    )
    
    print("\n🎉 V6 to ONNX Conversion Complete!")
    print("=" * 50)
    for key, value in results.items():
        if isinstance(value, float):
            print(f"{key}: {value:.2f}")
        else:
            print(f"{key}: {value}")
    
    print("\n🚀 Next steps:")
    print("1. Upload optimized ONNX model to HuggingFace")
    print("2. Update app.py to use ONNX V6 model")
    print("3. Test performance on HuggingFace Spaces")
    print("4. Compare accuracy with original model")
