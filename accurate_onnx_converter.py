"""
Accurate ONNX Converter for Ultimate V2 Model
Creates ONNX version with preserved accuracy using careful conversion techniques.
"""

import torch
import torch.onnx
import numpy as np
import os
import time
from ultimate_breakthrough_v2 import UltimateBreakthroughV2

def load_model(model_path, device):
    """Load and prepare model for ONNX conversion"""
    print("🚀 Loading Ultimate V2 model for accurate ONNX conversion...")
    
    model = UltimateBreakthroughV2()
    model.load_state_dict(torch.load(model_path, map_location=device, weights_only=True))
    
    # Set to evaluation mode and disable all training-specific operations
    model.eval()
    model.to(device)
    
    # Disable gradients completely
    for param in model.parameters():
        param.requires_grad = False
    
    # Ensure all batch norm layers are in eval mode
    for module in model.modules():
        if isinstance(module, (torch.nn.BatchNorm2d, torch.nn.Dropout, torch.nn.Dropout2d)):
            module.eval()
    
    print("✅ Model loaded and prepared for conversion")
    return model

def convert_with_high_precision(model, output_path, device):
    """Convert model to ONNX with maximum precision preservation"""
    print(f"🔄 Converting to high-precision ONNX: {output_path}")
    
    # Create dummy input with exact specifications
    dummy_input = torch.randn(1, 3, 256, 256, dtype=torch.float32, device=device)
    
    # Extensive warm-up to stabilize model
    print("🔥 Warming up model...")
    with torch.no_grad():
        for _ in range(10):
            _ = model(dummy_input)
    
    try:
        # Convert with precision-focused settings
        torch.onnx.export(
            model,                              # Model to export
            dummy_input,                        # Model input
            output_path,                        # Output path
            export_params=True,                 # Store trained parameters
            opset_version=11,                   # Stable ONNX version
            do_constant_folding=True,           # Optimize constants
            input_names=['input'],              # Input tensor name
            output_names=['output'],            # Output tensor name
            dynamic_axes={                      # Allow dynamic batch size
                'input': {0: 'batch_size'},
                'output': {0: 'batch_size'}
            },
            training=torch.onnx.TrainingMode.EVAL,  # Explicit eval mode
            keep_initializers_as_inputs=False,      # Optimize initializers
            verbose=False                           # Reduce output noise
        )
        
        # Verify file creation and size
        if os.path.exists(output_path):
            onnx_size = os.path.getsize(output_path) / (1024 * 1024)
            print(f"✅ ONNX conversion successful!")
            print(f"📁 File size: {onnx_size:.2f}MB")
            return True, onnx_size
        else:
            print("❌ ONNX file was not created")
            return False, 0
            
    except Exception as e:
        print(f"❌ ONNX conversion failed: {e}")
        import traceback
        traceback.print_exc()
        return False, 0

def validate_conversion_accuracy(pytorch_model, onnx_path, device, tolerance=1e-5):
    """Validate ONNX conversion accuracy with strict tolerance"""
    print(f"🧪 Validating ONNX accuracy (tolerance: {tolerance})")
    
    try:
        import onnxruntime as ort
        
        # Create ONNX session with CPU provider for deterministic results
        providers = ['CPUExecutionProvider']
        ort_session = ort.InferenceSession(onnx_path, providers=providers)
        
        # Move PyTorch model to CPU for fair comparison
        pytorch_model_cpu = pytorch_model.cpu()
        
        # Test with multiple deterministic inputs
        test_cases = [
            torch.zeros(1, 3, 256, 256, dtype=torch.float32),
            torch.ones(1, 3, 256, 256, dtype=torch.float32),
            torch.randn(1, 3, 256, 256, dtype=torch.float32, generator=torch.Generator().manual_seed(42)),
            torch.randn(1, 3, 256, 256, dtype=torch.float32, generator=torch.Generator().manual_seed(123)),
        ]
        
        max_differences = []
        mean_differences = []
        
        for i, test_input in enumerate(test_cases):
            # PyTorch inference
            with torch.no_grad():
                pytorch_output = pytorch_model_cpu(test_input).numpy()
            
            # ONNX inference
            ort_inputs = {ort_session.get_inputs()[0].name: test_input.numpy()}
            onnx_output = ort_session.run(None, ort_inputs)[0]
            
            # Calculate differences
            abs_diff = np.abs(pytorch_output - onnx_output)
            max_diff = abs_diff.max()
            mean_diff = abs_diff.mean()
            
            max_differences.append(max_diff)
            mean_differences.append(mean_diff)
            
            print(f"   Test {i+1}: Max diff = {max_diff:.8f}, Mean diff = {mean_diff:.8f}")
        
        overall_max_diff = max(max_differences)
        overall_mean_diff = np.mean(mean_differences)
        
        # Accuracy assessment
        if overall_max_diff < tolerance:
            accuracy_status = "✅ EXCELLENT - High accuracy preserved"
            passed = True
        elif overall_max_diff < tolerance * 10:
            accuracy_status = "✅ GOOD - Acceptable accuracy"
            passed = True
        elif overall_max_diff < tolerance * 100:
            accuracy_status = "⚠️ MODERATE - Some accuracy loss"
            passed = False
        else:
            accuracy_status = "❌ POOR - Significant accuracy loss"
            passed = False
        
        print(f"\n📊 Validation Results:")
        print(f"   Overall Max Difference: {overall_max_diff:.8f}")
        print(f"   Overall Mean Difference: {overall_mean_diff:.8f}")
        print(f"   Status: {accuracy_status}")
        
        return passed, overall_max_diff, overall_mean_diff
        
    except ImportError:
        print("⚠️ ONNX Runtime not installed. Install with: pip install onnxruntime")
        return False, 0, 0
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        return False, 0, 0

def benchmark_performance(pytorch_model, onnx_path, device):
    """Benchmark performance comparison"""
    print("⚡ Benchmarking performance...")
    
    try:
        import onnxruntime as ort
        
        # Setup
        providers = ['CPUExecutionProvider']
        ort_session = ort.InferenceSession(onnx_path, providers=providers)
        pytorch_model_cpu = pytorch_model.cpu()
        
        test_input = torch.randn(1, 3, 256, 256, dtype=torch.float32)
        
        # Warm up
        for _ in range(5):
            with torch.no_grad():
                _ = pytorch_model_cpu(test_input)
            ort_inputs = {ort_session.get_inputs()[0].name: test_input.numpy()}
            _ = ort_session.run(None, ort_inputs)
        
        # Benchmark PyTorch
        pytorch_times = []
        for _ in range(20):
            start_time = time.perf_counter()
            with torch.no_grad():
                _ = pytorch_model_cpu(test_input)
            pytorch_times.append((time.perf_counter() - start_time) * 1000)
        
        # Benchmark ONNX
        onnx_times = []
        for _ in range(20):
            start_time = time.perf_counter()
            ort_inputs = {ort_session.get_inputs()[0].name: test_input.numpy()}
            _ = ort_session.run(None, ort_inputs)
            onnx_times.append((time.perf_counter() - start_time) * 1000)
        
        pytorch_avg = np.mean(pytorch_times)
        onnx_avg = np.mean(onnx_times)
        speedup = pytorch_avg / onnx_avg
        
        print(f"📊 Performance Results:")
        print(f"   PyTorch: {pytorch_avg:.2f}ms ± {np.std(pytorch_times):.2f}ms")
        print(f"   ONNX: {onnx_avg:.2f}ms ± {np.std(onnx_times):.2f}ms")
        print(f"   Speedup: {speedup:.2f}x")
        
        return pytorch_avg, onnx_avg, speedup
        
    except Exception as e:
        print(f"❌ Benchmarking failed: {e}")
        return 0, 0, 0

def main():
    """Main accurate conversion function"""
    # Configuration
    model_path = "ultimate_v2_breakthrough/ultimate_v2_breakthrough.pth"
    output_dir = "ultimate_v2_breakthrough"
    onnx_filename = "ultimate_v2_breakthrough_accurate.onnx"
    
    # Check input
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    onnx_path = os.path.join(output_dir, onnx_filename)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Using device: {device}")
    
    try:
        # Load model
        model = load_model(model_path, device)
        
        # Convert to ONNX
        success, onnx_size = convert_with_high_precision(model, onnx_path, device)
        
        if success:
            # Validate accuracy
            passed, max_diff, mean_diff = validate_conversion_accuracy(model, onnx_path, device)
            
            # Benchmark performance
            pytorch_time, onnx_time, speedup = benchmark_performance(model, onnx_path, device)
            
            # Summary
            print("\n" + "="*70)
            print("🏆 ACCURATE ONNX CONVERSION SUMMARY")
            print("="*70)
            print(f"📁 Original Model: {model_path}")
            print(f"📁 ONNX Model: {onnx_path}")
            print(f"📊 ONNX Size: {onnx_size:.2f}MB")
            print(f"🎯 Max Accuracy Difference: {max_diff:.8f}")
            print(f"🎯 Mean Accuracy Difference: {mean_diff:.8f}")
            print(f"⚡ Performance: {onnx_time:.2f}ms ({speedup:.2f}x speedup)")
            
            if passed:
                print("✅ CONVERSION SUCCESSFUL - High accuracy preserved!")
                print("🚀 Ready for production deployment!")
            else:
                print("⚠️ CONVERSION COMPLETED - Please validate accuracy for your use case")
            
            print("="*70)
        
    except Exception as e:
        print(f"❌ Conversion failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
