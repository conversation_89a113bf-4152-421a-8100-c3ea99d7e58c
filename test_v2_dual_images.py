"""
Test V2 Distilled Model and ONNX Version on Two Chess Images
Comprehensive comparison of PyTorch vs ONNX performance and accuracy on multiple images.
"""

import torch
import numpy as np
import cv2
import os
import time
import matplotlib
matplotlib.use('Agg')  # Non-interactive backend
import matplotlib.pyplot as plt
from ultimate_breakthrough_v2 import UltimateBreakthroughV2

def load_models(pytorch_path, onnx_path, device):
    """Load both PyTorch and ONNX models"""
    print("📥 Loading V2 models...")
    
    # Load PyTorch model
    pytorch_model = UltimateBreakthroughV2()
    pytorch_model.load_state_dict(torch.load(pytorch_path, map_location=device, weights_only=True))
    pytorch_model.eval()
    pytorch_model.to(device)
    
    params = sum(p.numel() for p in pytorch_model.parameters())
    size_mb = sum(p.numel() * p.element_size() for p in pytorch_model.parameters()) / (1024 * 1024)
    print(f"✅ PyTorch V2: {params:,} parameters, {size_mb:.2f}MB")
    
    # Load ONNX model
    try:
        import onnxruntime as ort
        providers = ['CPUExecutionProvider']
        onnx_session = ort.InferenceSession(onnx_path, providers=providers)
        onnx_size = os.path.getsize(onnx_path) / (1024 * 1024)
        print(f"✅ ONNX V2: {onnx_size:.2f}MB")
        return pytorch_model, onnx_session
    except ImportError:
        print("❌ ONNX Runtime not installed")
        return pytorch_model, None

def preprocess_image(image_path):
    """Preprocess chess board image for model input"""
    print(f"🖼️ Processing: {os.path.basename(image_path)}")
    
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image: {image_path}")
    
    # Convert to RGB
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    original_shape = image_rgb.shape[:2]
    
    # Resize to model input size (256x256)
    image_resized = cv2.resize(image_rgb, (256, 256))
    
    # Normalize to [0, 1]
    image_normalized = image_resized.astype(np.float32) / 255.0
    
    # Convert to tensor format
    image_tensor = torch.from_numpy(image_normalized).permute(2, 0, 1).unsqueeze(0)
    
    return image_tensor, image_rgb, original_shape

def run_model_inference(pytorch_model, onnx_session, image_tensor, device, num_runs=5):
    """Run inference on both models with timing"""
    print("🚀 Running inference comparison...")
    
    # Move PyTorch model to CPU for fair comparison with ONNX
    pytorch_model_cpu = pytorch_model.cpu()
    
    # PyTorch inference with timing
    pytorch_times = []
    for i in range(num_runs):
        start_time = time.perf_counter()
        with torch.no_grad():
            pytorch_output = pytorch_model_cpu(image_tensor)
        pytorch_times.append((time.perf_counter() - start_time) * 1000)
    
    pytorch_avg_time = np.mean(pytorch_times)
    pytorch_mask = torch.sigmoid(pytorch_output).squeeze().numpy()
    
    # ONNX inference with timing
    onnx_times = []
    ort_inputs = {onnx_session.get_inputs()[0].name: image_tensor.numpy()}
    
    for i in range(num_runs):
        start_time = time.perf_counter()
        onnx_outputs = onnx_session.run(None, ort_inputs)
        onnx_times.append((time.perf_counter() - start_time) * 1000)
    
    onnx_avg_time = np.mean(onnx_times)
    onnx_raw = onnx_outputs[0].squeeze()
    onnx_mask = 1.0 / (1.0 + np.exp(-onnx_raw))  # Manual sigmoid
    
    # Calculate accuracy differences
    abs_diff = np.abs(pytorch_mask - onnx_mask)
    max_diff = abs_diff.max()
    mean_diff = abs_diff.mean()
    
    # Calculate Dice similarity
    pytorch_binary = (pytorch_mask > 0.5).astype(np.float32)
    onnx_binary = (onnx_mask > 0.5).astype(np.float32)
    intersection = np.sum(pytorch_binary * onnx_binary)
    union = np.sum(pytorch_binary) + np.sum(onnx_binary)
    dice_score = (2.0 * intersection) / union if union > 0 else 1.0
    
    speedup = pytorch_avg_time / onnx_avg_time
    
    print(f"   PyTorch: {pytorch_avg_time:.2f}ms ± {np.std(pytorch_times):.2f}ms")
    print(f"   ONNX: {onnx_avg_time:.2f}ms ± {np.std(onnx_times):.2f}ms")
    print(f"   Speedup: {speedup:.2f}x")
    print(f"   Max Diff: {max_diff:.6f}")
    print(f"   Dice Score: {dice_score:.4f}")
    
    return {
        'pytorch_mask': pytorch_mask,
        'onnx_mask': onnx_mask,
        'pytorch_time': pytorch_avg_time,
        'onnx_time': onnx_avg_time,
        'speedup': speedup,
        'max_diff': max_diff,
        'mean_diff': mean_diff,
        'dice_score': dice_score
    }

def create_dual_image_visualization(image1_data, image2_data, results1, results2):
    """Create comprehensive visualization for both images"""
    print("🎨 Creating dual image comparison visualization...")
    
    fig, axes = plt.subplots(4, 4, figsize=(20, 20))
    fig.suptitle('V2 Distilled Model: PyTorch vs ONNX Comparison\nTwo Chess Board Images', 
                 fontsize=18, fontweight='bold')
    
    # Image 1 - Row 1: Original and PyTorch
    axes[0, 0].imshow(image1_data['original'])
    axes[0, 0].set_title(f'Image 1: {os.path.basename(image1_data["path"])}', fontweight='bold')
    axes[0, 0].axis('off')
    
    pytorch_mask1_resized = cv2.resize(results1['pytorch_mask'], 
                                      (image1_data['original'].shape[1], image1_data['original'].shape[0]))
    axes[0, 1].imshow(pytorch_mask1_resized, cmap='hot')
    axes[0, 1].set_title(f'PyTorch Heatmap\n{results1["pytorch_time"]:.2f}ms', fontweight='bold')
    axes[0, 1].axis('off')
    
    pytorch_binary1 = (pytorch_mask1_resized > 0.5).astype(np.uint8) * 255
    overlay1_pytorch = image1_data['original'].copy()
    overlay1_pytorch[pytorch_binary1 > 0] = [255, 0, 0]
    axes[0, 2].imshow(overlay1_pytorch)
    axes[0, 2].set_title('PyTorch Overlay (Red)', fontweight='bold')
    axes[0, 2].axis('off')
    
    # Image 1 - Row 2: ONNX
    axes[1, 0].imshow(image1_data['original'])
    axes[1, 0].set_title('Same Image', fontweight='bold')
    axes[1, 0].axis('off')
    
    onnx_mask1_resized = cv2.resize(results1['onnx_mask'], 
                                   (image1_data['original'].shape[1], image1_data['original'].shape[0]))
    axes[1, 1].imshow(onnx_mask1_resized, cmap='hot')
    axes[1, 1].set_title(f'ONNX Heatmap\n{results1["onnx_time"]:.2f}ms ({results1["speedup"]:.2f}x)', fontweight='bold')
    axes[1, 1].axis('off')
    
    onnx_binary1 = (onnx_mask1_resized > 0.5).astype(np.uint8) * 255
    overlay1_onnx = image1_data['original'].copy()
    overlay1_onnx[onnx_binary1 > 0] = [0, 255, 0]
    axes[1, 2].imshow(overlay1_onnx)
    axes[1, 2].set_title('ONNX Overlay (Green)', fontweight='bold')
    axes[1, 2].axis('off')
    
    # Difference visualization for Image 1
    diff_mask1 = np.abs(pytorch_mask1_resized - onnx_mask1_resized)
    axes[1, 3].imshow(diff_mask1, cmap='Reds')
    axes[1, 3].set_title(f'Difference Map\nMax: {results1["max_diff"]:.4f}', fontweight='bold')
    axes[1, 3].axis('off')
    
    # Image 2 - Row 3: Original and PyTorch
    axes[2, 0].imshow(image2_data['original'])
    axes[2, 0].set_title(f'Image 2: {os.path.basename(image2_data["path"])}', fontweight='bold')
    axes[2, 0].axis('off')
    
    pytorch_mask2_resized = cv2.resize(results2['pytorch_mask'], 
                                      (image2_data['original'].shape[1], image2_data['original'].shape[0]))
    axes[2, 1].imshow(pytorch_mask2_resized, cmap='hot')
    axes[2, 1].set_title(f'PyTorch Heatmap\n{results2["pytorch_time"]:.2f}ms', fontweight='bold')
    axes[2, 1].axis('off')
    
    pytorch_binary2 = (pytorch_mask2_resized > 0.5).astype(np.uint8) * 255
    overlay2_pytorch = image2_data['original'].copy()
    overlay2_pytorch[pytorch_binary2 > 0] = [255, 0, 0]
    axes[2, 2].imshow(overlay2_pytorch)
    axes[2, 2].set_title('PyTorch Overlay (Red)', fontweight='bold')
    axes[2, 2].axis('off')
    
    # Image 2 - Row 4: ONNX
    axes[3, 0].imshow(image2_data['original'])
    axes[3, 0].set_title('Same Image', fontweight='bold')
    axes[3, 0].axis('off')
    
    onnx_mask2_resized = cv2.resize(results2['onnx_mask'], 
                                   (image2_data['original'].shape[1], image2_data['original'].shape[0]))
    axes[3, 1].imshow(onnx_mask2_resized, cmap='hot')
    axes[3, 1].set_title(f'ONNX Heatmap\n{results2["onnx_time"]:.2f}ms ({results2["speedup"]:.2f}x)', fontweight='bold')
    axes[3, 1].axis('off')
    
    onnx_binary2 = (onnx_mask2_resized > 0.5).astype(np.uint8) * 255
    overlay2_onnx = image2_data['original'].copy()
    overlay2_onnx[onnx_binary2 > 0] = [0, 255, 0]
    axes[3, 2].imshow(overlay2_onnx)
    axes[3, 2].set_title('ONNX Overlay (Green)', fontweight='bold')
    axes[3, 2].axis('off')
    
    # Difference visualization for Image 2
    diff_mask2 = np.abs(pytorch_mask2_resized - onnx_mask2_resized)
    axes[3, 3].imshow(diff_mask2, cmap='Reds')
    axes[3, 3].set_title(f'Difference Map\nMax: {results2["max_diff"]:.4f}', fontweight='bold')
    axes[3, 3].axis('off')
    
    # Add accuracy info to remaining cells
    axes[0, 3].text(0.1, 0.5, f'Image 1 Accuracy:\nDice Score: {results1["dice_score"]:.4f}\nMax Diff: {results1["max_diff"]:.6f}\nMean Diff: {results1["mean_diff"]:.6f}', 
                   transform=axes[0, 3].transAxes, fontsize=12, verticalalignment='center',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
    axes[0, 3].axis('off')
    
    axes[2, 3].text(0.1, 0.5, f'Image 2 Accuracy:\nDice Score: {results2["dice_score"]:.4f}\nMax Diff: {results2["max_diff"]:.6f}\nMean Diff: {results2["mean_diff"]:.6f}', 
                   transform=axes[2, 3].transAxes, fontsize=12, verticalalignment='center',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen"))
    axes[2, 3].axis('off')
    
    plt.tight_layout()
    
    # Save visualization
    output_path = "v2_dual_image_comparison.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"📸 Dual image visualization saved: {output_path}")
    return output_path

def print_comprehensive_results(results1, results2, image1_path, image2_path):
    """Print comprehensive comparison results"""
    print("\n" + "="*80)
    print("🏆 V2 DISTILLED MODEL: PYTORCH vs ONNX COMPREHENSIVE RESULTS")
    print("="*80)

    print(f"📁 Image 1: {os.path.basename(image1_path)}")
    print(f"📁 Image 2: {os.path.basename(image2_path)}")
    print()

    print("⚡ PERFORMANCE COMPARISON:")
    print(f"   Image 1 - PyTorch: {results1['pytorch_time']:.2f}ms | ONNX: {results1['onnx_time']:.2f}ms | Speedup: {results1['speedup']:.2f}x")
    print(f"   Image 2 - PyTorch: {results2['pytorch_time']:.2f}ms | ONNX: {results2['onnx_time']:.2f}ms | Speedup: {results2['speedup']:.2f}x")

    avg_pytorch_time = (results1['pytorch_time'] + results2['pytorch_time']) / 2
    avg_onnx_time = (results1['onnx_time'] + results2['onnx_time']) / 2
    avg_speedup = avg_pytorch_time / avg_onnx_time

    print(f"   Average - PyTorch: {avg_pytorch_time:.2f}ms | ONNX: {avg_onnx_time:.2f}ms | Speedup: {avg_speedup:.2f}x")
    print()

    print("🎯 ACCURACY COMPARISON:")
    print(f"   Image 1 - Max Diff: {results1['max_diff']:.6f} | Dice Score: {results1['dice_score']:.4f}")
    print(f"   Image 2 - Max Diff: {results2['max_diff']:.6f} | Dice Score: {results2['dice_score']:.4f}")

    avg_max_diff = (results1['max_diff'] + results2['max_diff']) / 2
    avg_dice = (results1['dice_score'] + results2['dice_score']) / 2

    print(f"   Average - Max Diff: {avg_max_diff:.6f} | Dice Score: {avg_dice:.4f}")
    print()

    # Overall assessment
    print("📋 OVERALL ASSESSMENT:")

    # Performance assessment
    if avg_speedup > 3:
        perf_status = "🏆 EXCELLENT speedup"
    elif avg_speedup > 2:
        perf_status = "✅ VERY GOOD speedup"
    elif avg_speedup > 1.5:
        perf_status = "✅ GOOD speedup"
    else:
        perf_status = "⚠️ MODERATE speedup"

    # Accuracy assessment
    if avg_max_diff < 0.001 and avg_dice > 0.99:
        acc_status = "🏆 EXCELLENT accuracy preservation"
    elif avg_max_diff < 0.01 and avg_dice > 0.95:
        acc_status = "✅ VERY GOOD accuracy preservation"
    elif avg_max_diff < 0.05 and avg_dice > 0.90:
        acc_status = "✅ GOOD accuracy preservation"
    else:
        acc_status = "⚠️ MODERATE accuracy preservation"

    print(f"   Performance: {perf_status}")
    print(f"   Accuracy: {acc_status}")

    # Final recommendation
    if avg_speedup > 2 and avg_max_diff < 0.01:
        print("\n🎉 BREAKTHROUGH SUCCESS!")
        print("   ✅ ONNX version provides excellent speedup with high accuracy")
        print("   ✅ RECOMMENDED for production chess applications")
        print("   ✅ Perfect balance of speed and accuracy")
    elif avg_speedup > 1.5 and avg_max_diff < 0.05:
        print("\n✅ SUCCESS!")
        print("   ✅ ONNX version provides good improvements")
        print("   ✅ SUITABLE for production use")
    else:
        print("\n⚠️ MIXED RESULTS")
        print("   ⚠️ Consider trade-offs between speed and accuracy")
        print("   ⚠️ Test thoroughly in your specific application")

    print("="*80)

def main():
    """Main testing function for dual images"""
    # Configuration
    pytorch_path = "ultimate_v2_breakthrough/ultimate_v2_breakthrough.pth"
    onnx_path = "ultimate_v2_breakthrough/ultimate_v2_breakthrough_accurate.onnx"

    image1_path = r"C:\Users\<USER>\Downloads\24.jpg"
    image2_path = r"C:\Users\<USER>\OneDrive\Desktop\New folder (4)\24.jpg"

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Using device: {device}")
    print()

    try:
        # Check if files exist
        for path in [pytorch_path, onnx_path, image1_path, image2_path]:
            if not os.path.exists(path):
                print(f"❌ File not found: {path}")
                return

        # Load models
        pytorch_model, onnx_session = load_models(pytorch_path, onnx_path, device)

        if onnx_session is None:
            return

        print("\n🖼️ PROCESSING IMAGE 1:")
        print("="*50)
        # Process Image 1
        image1_tensor, image1_rgb, image1_shape = preprocess_image(image1_path)
        results1 = run_model_inference(pytorch_model, onnx_session, image1_tensor, device)

        print("\n🖼️ PROCESSING IMAGE 2:")
        print("="*50)
        # Process Image 2
        image2_tensor, image2_rgb, image2_shape = preprocess_image(image2_path)
        results2 = run_model_inference(pytorch_model, onnx_session, image2_tensor, device)

        # Create visualization
        print("\n🎨 CREATING VISUALIZATION:")
        print("="*50)
        image1_data = {'original': image1_rgb, 'path': image1_path}
        image2_data = {'original': image2_rgb, 'path': image2_path}

        viz_path = create_dual_image_visualization(image1_data, image2_data, results1, results2)

        # Print comprehensive results
        print_comprehensive_results(results1, results2, image1_path, image2_path)

        print(f"\n📸 Comprehensive visualization: {viz_path}")
        print("\n🚀 Testing completed successfully!")

    except Exception as e:
        print(f"❌ Testing failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
