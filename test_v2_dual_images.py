"""
Test V2 Distilled Model and ONNX Version on Two Chess Images
Comprehensive comparison of PyTorch vs ONNX performance and accuracy on multiple images.
"""

import torch
import numpy as np
import cv2
import os
import time
import matplotlib
matplotlib.use('Agg')  # Non-interactive backend
import matplotlib.pyplot as plt
from ultimate_breakthrough_v2 import UltimateBreakthroughV2

def load_models(pytorch_path, onnx_path, device):
    """Load both PyTorch and ONNX models"""
    print("📥 Loading V2 models...")
    
    # Load PyTorch model
    pytorch_model = UltimateBreakthroughV2()
    pytorch_model.load_state_dict(torch.load(pytorch_path, map_location=device, weights_only=True))
    pytorch_model.eval()
    pytorch_model.to(device)
    
    params = sum(p.numel() for p in pytorch_model.parameters())
    size_mb = sum(p.numel() * p.element_size() for p in pytorch_model.parameters()) / (1024 * 1024)
    print(f"✅ PyTorch V2: {params:,} parameters, {size_mb:.2f}MB")
    
    # Load ONNX model
    try:
        import onnxruntime as ort
        providers = ['CPUExecutionProvider']
        onnx_session = ort.InferenceSession(onnx_path, providers=providers)
        onnx_size = os.path.getsize(onnx_path) / (1024 * 1024)
        print(f"✅ ONNX V2: {onnx_size:.2f}MB")
        return pytorch_model, onnx_session
    except ImportError:
        print("❌ ONNX Runtime not installed")
        return pytorch_model, None

def preprocess_image(image_path):
    """Preprocess chess board image for model input"""
    print(f"🖼️ Processing: {os.path.basename(image_path)}")
    
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image: {image_path}")
    
    # Convert to RGB
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    original_shape = image_rgb.shape[:2]
    
    # Resize to model input size (256x256)
    image_resized = cv2.resize(image_rgb, (256, 256))
    
    # Normalize to [0, 1]
    image_normalized = image_resized.astype(np.float32) / 255.0
    
    # Convert to tensor format
    image_tensor = torch.from_numpy(image_normalized).permute(2, 0, 1).unsqueeze(0)
    
    return image_tensor, image_rgb, original_shape

def run_model_inference(pytorch_model, onnx_session, image_tensor, device, num_runs=5):
    """Run inference on both models with timing"""
    print("🚀 Running inference comparison...")
    
    # Move PyTorch model to CPU for fair comparison with ONNX
    pytorch_model_cpu = pytorch_model.cpu()
    
    # PyTorch inference with timing
    pytorch_times = []
    for i in range(num_runs):
        start_time = time.perf_counter()
        with torch.no_grad():
            pytorch_output = pytorch_model_cpu(image_tensor)
        pytorch_times.append((time.perf_counter() - start_time) * 1000)
    
    pytorch_avg_time = np.mean(pytorch_times)
    pytorch_mask = torch.sigmoid(pytorch_output).squeeze().numpy()
    
    # ONNX inference with timing
    onnx_times = []
    ort_inputs = {onnx_session.get_inputs()[0].name: image_tensor.numpy()}
    
    for i in range(num_runs):
        start_time = time.perf_counter()
        onnx_outputs = onnx_session.run(None, ort_inputs)
        onnx_times.append((time.perf_counter() - start_time) * 1000)
    
    onnx_avg_time = np.mean(onnx_times)
    onnx_raw = onnx_outputs[0].squeeze()
    onnx_mask = 1.0 / (1.0 + np.exp(-onnx_raw))  # Manual sigmoid
    
    # Calculate accuracy differences
    abs_diff = np.abs(pytorch_mask - onnx_mask)
    max_diff = abs_diff.max()
    mean_diff = abs_diff.mean()
    
    # Calculate Dice similarity
    pytorch_binary = (pytorch_mask > 0.5).astype(np.float32)
    onnx_binary = (onnx_mask > 0.5).astype(np.float32)
    intersection = np.sum(pytorch_binary * onnx_binary)
    union = np.sum(pytorch_binary) + np.sum(onnx_binary)
    dice_score = (2.0 * intersection) / union if union > 0 else 1.0
    
    speedup = pytorch_avg_time / onnx_avg_time
    
    print(f"   PyTorch: {pytorch_avg_time:.2f}ms ± {np.std(pytorch_times):.2f}ms")
    print(f"   ONNX: {onnx_avg_time:.2f}ms ± {np.std(onnx_times):.2f}ms")
    print(f"   Speedup: {speedup:.2f}x")
    print(f"   Max Diff: {max_diff:.6f}")
    print(f"   Dice Score: {dice_score:.4f}")
    
    return {
        'pytorch_mask': pytorch_mask,
        'onnx_mask': onnx_mask,
        'pytorch_time': pytorch_avg_time,
        'onnx_time': onnx_avg_time,
        'speedup': speedup,
        'max_diff': max_diff,
        'mean_diff': mean_diff,
        'dice_score': dice_score
    }

def create_individual_visualizations(image_data, results, image_name):
    """Create individual visualization like the previous format"""
    print(f"🎨 Creating visualization for {image_name}...")

    original_image = image_data['original']
    pytorch_mask = results['pytorch_mask']
    onnx_mask = results['onnx_mask']

    # Resize masks to original image size
    original_h, original_w = original_image.shape[:2]
    pytorch_resized = cv2.resize(pytorch_mask, (original_w, original_h))
    onnx_resized = cv2.resize(onnx_mask, (original_w, original_h))

    # Create binary masks
    pytorch_binary = (pytorch_resized > 0.5).astype(np.uint8) * 255
    onnx_binary = (onnx_resized > 0.5).astype(np.uint8) * 255

    # Create visualization in the same format as before
    fig, axes = plt.subplots(2, 4, figsize=(20, 10))
    fig.suptitle(f'PyTorch vs ONNX V2 Model Comparison\n{image_name}',
                 fontsize=16, fontweight='bold')

    # Row 1: PyTorch Results
    axes[0, 0].imshow(original_image)
    axes[0, 0].set_title('Original Image', fontweight='bold')
    axes[0, 0].axis('off')

    axes[0, 1].imshow(pytorch_resized, cmap='hot', alpha=0.8)
    axes[0, 1].set_title(f'PyTorch Heatmap\nTime: {results["pytorch_time"]:.2f}ms', fontweight='bold')
    axes[0, 1].axis('off')

    axes[0, 2].imshow(pytorch_binary, cmap='gray')
    axes[0, 2].set_title('PyTorch Binary Mask', fontweight='bold')
    axes[0, 2].axis('off')

    # PyTorch overlay on original
    overlay_pytorch = original_image.copy()
    overlay_pytorch[pytorch_binary > 0] = [255, 0, 0]  # Red overlay
    axes[0, 3].imshow(overlay_pytorch)
    axes[0, 3].set_title('PyTorch Overlay (Red)', fontweight='bold')
    axes[0, 3].axis('off')

    # Row 2: ONNX Results
    axes[1, 0].imshow(original_image)
    axes[1, 0].set_title('Original Image', fontweight='bold')
    axes[1, 0].axis('off')

    axes[1, 1].imshow(onnx_resized, cmap='hot', alpha=0.8)
    axes[1, 1].set_title(f'ONNX Heatmap\nTime: {results["onnx_time"]:.2f}ms ({results["speedup"]:.2f}x)', fontweight='bold')
    axes[1, 1].axis('off')

    axes[1, 2].imshow(onnx_binary, cmap='gray')
    axes[1, 2].set_title('ONNX Binary Mask', fontweight='bold')
    axes[1, 2].axis('off')

    # ONNX overlay on original
    overlay_onnx = original_image.copy()
    overlay_onnx[onnx_binary > 0] = [0, 255, 0]  # Green overlay
    axes[1, 3].imshow(overlay_onnx)
    axes[1, 3].set_title('ONNX Overlay (Green)', fontweight='bold')
    axes[1, 3].axis('off')

    plt.tight_layout()

    # Save visualization
    safe_name = image_name.replace('.jpg', '').replace(' ', '_').replace('\\', '_').replace('/', '_')
    output_path = f"v2_comparison_{safe_name}.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"📸 Visualization saved: {output_path}")
    return output_path

def create_side_by_side_comparison(image1_data, image2_data, results1, results2):
    """Create side-by-side comparison of both images"""
    print("🎨 Creating side-by-side comparison...")

    fig, axes = plt.subplots(2, 6, figsize=(24, 8))
    fig.suptitle('V2 Model: PyTorch vs ONNX - Two Chess Images Comparison',
                 fontsize=16, fontweight='bold')

    # Process both images
    for idx, (image_data, results, img_name) in enumerate([(image1_data, results1, "Image 1"),
                                                           (image2_data, results2, "Image 2")]):
        original_image = image_data['original']
        pytorch_mask = results['pytorch_mask']
        onnx_mask = results['onnx_mask']

        # Resize masks
        original_h, original_w = original_image.shape[:2]
        pytorch_resized = cv2.resize(pytorch_mask, (original_w, original_h))
        onnx_resized = cv2.resize(onnx_mask, (original_w, original_h))

        # Create binary masks
        pytorch_binary = (pytorch_resized > 0.5).astype(np.uint8) * 255
        onnx_binary = (onnx_resized > 0.5).astype(np.uint8) * 255

        # Create overlays
        overlay_pytorch = original_image.copy()
        overlay_pytorch[pytorch_binary > 0] = [255, 0, 0]  # Red

        overlay_onnx = original_image.copy()
        overlay_onnx[onnx_binary > 0] = [0, 255, 0]  # Green

        # Plot for this image
        col_start = idx * 3

        # Original
        axes[0, col_start].imshow(original_image)
        axes[0, col_start].set_title(f'{img_name}\n{os.path.basename(image_data["path"])}', fontweight='bold')
        axes[0, col_start].axis('off')

        # PyTorch overlay
        axes[0, col_start + 1].imshow(overlay_pytorch)
        axes[0, col_start + 1].set_title(f'PyTorch (Red)\n{results["pytorch_time"]:.2f}ms', fontweight='bold')
        axes[0, col_start + 1].axis('off')

        # ONNX overlay
        axes[0, col_start + 2].imshow(overlay_onnx)
        axes[0, col_start + 2].set_title(f'ONNX (Green)\n{results["onnx_time"]:.2f}ms ({results["speedup"]:.2f}x)', fontweight='bold')
        axes[0, col_start + 2].axis('off')

        # Heatmaps in second row
        axes[1, col_start].imshow(pytorch_resized, cmap='hot')
        axes[1, col_start].set_title('PyTorch Heatmap', fontweight='bold')
        axes[1, col_start].axis('off')

        axes[1, col_start + 1].imshow(onnx_resized, cmap='hot')
        axes[1, col_start + 1].set_title('ONNX Heatmap', fontweight='bold')
        axes[1, col_start + 1].axis('off')

        # Difference map
        diff_mask = np.abs(pytorch_resized - onnx_resized)
        axes[1, col_start + 2].imshow(diff_mask, cmap='Reds')
        axes[1, col_start + 2].set_title(f'Difference\nMax: {results["max_diff"]:.6f}', fontweight='bold')
        axes[1, col_start + 2].axis('off')

    plt.tight_layout()

    # Save side-by-side
    output_path = "v2_side_by_side_comparison.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"📸 Side-by-side comparison saved: {output_path}")
    return output_path

def print_comprehensive_results(results1, results2, image1_path, image2_path):
    """Print comprehensive comparison results"""
    print("\n" + "="*80)
    print("🏆 V2 DISTILLED MODEL: PYTORCH vs ONNX COMPREHENSIVE RESULTS")
    print("="*80)

    print(f"📁 Image 1: {os.path.basename(image1_path)}")
    print(f"📁 Image 2: {os.path.basename(image2_path)}")
    print()

    print("⚡ PERFORMANCE COMPARISON:")
    print(f"   Image 1 - PyTorch: {results1['pytorch_time']:.2f}ms | ONNX: {results1['onnx_time']:.2f}ms | Speedup: {results1['speedup']:.2f}x")
    print(f"   Image 2 - PyTorch: {results2['pytorch_time']:.2f}ms | ONNX: {results2['onnx_time']:.2f}ms | Speedup: {results2['speedup']:.2f}x")

    avg_pytorch_time = (results1['pytorch_time'] + results2['pytorch_time']) / 2
    avg_onnx_time = (results1['onnx_time'] + results2['onnx_time']) / 2
    avg_speedup = avg_pytorch_time / avg_onnx_time

    print(f"   Average - PyTorch: {avg_pytorch_time:.2f}ms | ONNX: {avg_onnx_time:.2f}ms | Speedup: {avg_speedup:.2f}x")
    print()

    print("🎯 ACCURACY COMPARISON:")
    print(f"   Image 1 - Max Diff: {results1['max_diff']:.6f} | Dice Score: {results1['dice_score']:.4f}")
    print(f"   Image 2 - Max Diff: {results2['max_diff']:.6f} | Dice Score: {results2['dice_score']:.4f}")

    avg_max_diff = (results1['max_diff'] + results2['max_diff']) / 2
    avg_dice = (results1['dice_score'] + results2['dice_score']) / 2

    print(f"   Average - Max Diff: {avg_max_diff:.6f} | Dice Score: {avg_dice:.4f}")
    print()

    # Overall assessment
    print("📋 OVERALL ASSESSMENT:")

    # Performance assessment
    if avg_speedup > 3:
        perf_status = "🏆 EXCELLENT speedup"
    elif avg_speedup > 2:
        perf_status = "✅ VERY GOOD speedup"
    elif avg_speedup > 1.5:
        perf_status = "✅ GOOD speedup"
    else:
        perf_status = "⚠️ MODERATE speedup"

    # Accuracy assessment
    if avg_max_diff < 0.001 and avg_dice > 0.99:
        acc_status = "🏆 EXCELLENT accuracy preservation"
    elif avg_max_diff < 0.01 and avg_dice > 0.95:
        acc_status = "✅ VERY GOOD accuracy preservation"
    elif avg_max_diff < 0.05 and avg_dice > 0.90:
        acc_status = "✅ GOOD accuracy preservation"
    else:
        acc_status = "⚠️ MODERATE accuracy preservation"

    print(f"   Performance: {perf_status}")
    print(f"   Accuracy: {acc_status}")

    # Final recommendation
    if avg_speedup > 2 and avg_max_diff < 0.01:
        print("\n🎉 BREAKTHROUGH SUCCESS!")
        print("   ✅ ONNX version provides excellent speedup with high accuracy")
        print("   ✅ RECOMMENDED for production chess applications")
        print("   ✅ Perfect balance of speed and accuracy")
    elif avg_speedup > 1.5 and avg_max_diff < 0.05:
        print("\n✅ SUCCESS!")
        print("   ✅ ONNX version provides good improvements")
        print("   ✅ SUITABLE for production use")
    else:
        print("\n⚠️ MIXED RESULTS")
        print("   ⚠️ Consider trade-offs between speed and accuracy")
        print("   ⚠️ Test thoroughly in your specific application")

    print("="*80)

def main():
    """Main testing function for dual images"""
    # Configuration
    pytorch_path = "ultimate_v2_breakthrough/ultimate_v2_breakthrough.pth"
    onnx_path = "ultimate_v2_breakthrough/ultimate_v2_breakthrough_accurate.onnx"

    image1_path = r"C:\Users\<USER>\Downloads\24.jpg"
    image2_path = r"C:\Users\<USER>\OneDrive\Desktop\New folder (4)\24.jpg"

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Using device: {device}")
    print()

    try:
        # Check if files exist
        for path in [pytorch_path, onnx_path, image1_path, image2_path]:
            if not os.path.exists(path):
                print(f"❌ File not found: {path}")
                return

        # Load models
        pytorch_model, onnx_session = load_models(pytorch_path, onnx_path, device)

        if onnx_session is None:
            return

        print("\n🖼️ PROCESSING IMAGE 1:")
        print("="*50)
        # Process Image 1
        image1_tensor, image1_rgb, image1_shape = preprocess_image(image1_path)
        results1 = run_model_inference(pytorch_model, onnx_session, image1_tensor, device)

        print("\n🖼️ PROCESSING IMAGE 2:")
        print("="*50)
        # Process Image 2
        image2_tensor, image2_rgb, image2_shape = preprocess_image(image2_path)
        results2 = run_model_inference(pytorch_model, onnx_session, image2_tensor, device)

        # Create visualizations
        print("\n🎨 CREATING VISUALIZATIONS:")
        print("="*50)
        image1_data = {'original': image1_rgb, 'path': image1_path}
        image2_data = {'original': image2_rgb, 'path': image2_path}

        # Create individual visualizations (like before)
        viz1_path = create_individual_visualizations(image1_data, results1, f"Image 1 - {os.path.basename(image1_path)}")
        viz2_path = create_individual_visualizations(image2_data, results2, f"Image 2 - {os.path.basename(image2_path)}")

        # Create side-by-side comparison
        side_by_side_path = create_side_by_side_comparison(image1_data, image2_data, results1, results2)

        # Print comprehensive results
        print_comprehensive_results(results1, results2, image1_path, image2_path)

        print(f"\n📸 Individual visualizations:")
        print(f"   - Image 1: {viz1_path}")
        print(f"   - Image 2: {viz2_path}")
        print(f"📸 Side-by-side comparison: {side_by_side_path}")
        print("\n🚀 Testing completed successfully!")

    except Exception as e:
        print(f"❌ Testing failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
