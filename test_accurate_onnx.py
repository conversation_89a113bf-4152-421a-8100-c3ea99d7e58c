"""
Test Accurate ONNX Model on Real Chess Image
Tests the accurate ONNX conversion on your chess board image.
"""

import torch
import numpy as np
import cv2
import os
import time
import matplotlib
matplotlib.use('Agg')  # Non-interactive backend
import matplotlib.pyplot as plt
from ultimate_breakthrough_v2 import UltimateBreakthroughV2

def load_models(pytorch_path, onnx_path, device):
    """Load both PyTorch and ONNX models"""
    print("📥 Loading models...")
    
    # Load PyTorch model
    pytorch_model = UltimateBreakthroughV2()
    pytorch_model.load_state_dict(torch.load(pytorch_path, map_location=device, weights_only=True))
    pytorch_model.eval()
    pytorch_model.to(device)
    
    # Load ONNX model
    try:
        import onnxruntime as ort
        providers = ['CPUExecutionProvider']
        onnx_session = ort.InferenceSession(onnx_path, providers=providers)
        print("✅ Both models loaded successfully")
        return pytorch_model, onnx_session
    except ImportError:
        print("❌ ONNX Runtime not installed")
        return pytorch_model, None

def preprocess_image(image_path):
    """Preprocess chess board image"""
    print(f"🖼️ Processing image: {os.path.basename(image_path)}")
    
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image: {image_path}")
    
    # Convert to RGB
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    original_shape = image_rgb.shape[:2]
    
    # Resize to model input size
    image_resized = cv2.resize(image_rgb, (256, 256))
    
    # Normalize
    image_normalized = image_resized.astype(np.float32) / 255.0
    
    # Convert to tensor
    image_tensor = torch.from_numpy(image_normalized).permute(2, 0, 1).unsqueeze(0)
    
    return image_tensor, image_rgb, original_shape

def run_inference_comparison(pytorch_model, onnx_session, image_tensor, device):
    """Run inference on both models and compare"""
    print("🚀 Running inference comparison...")
    
    # PyTorch inference
    pytorch_model_cpu = pytorch_model.cpu()  # Move to CPU for fair comparison
    
    start_time = time.perf_counter()
    with torch.no_grad():
        pytorch_output = pytorch_model_cpu(image_tensor)
    pytorch_time = (time.perf_counter() - start_time) * 1000
    
    # Apply sigmoid to get probabilities
    pytorch_mask = torch.sigmoid(pytorch_output).squeeze().numpy()
    
    # ONNX inference
    ort_inputs = {onnx_session.get_inputs()[0].name: image_tensor.numpy()}
    
    start_time = time.perf_counter()
    onnx_outputs = onnx_session.run(None, ort_inputs)
    onnx_time = (time.perf_counter() - start_time) * 1000
    
    # Apply sigmoid to ONNX output
    onnx_raw = onnx_outputs[0].squeeze()
    onnx_mask = 1.0 / (1.0 + np.exp(-onnx_raw))  # Manual sigmoid
    
    # Calculate differences
    abs_diff = np.abs(pytorch_mask - onnx_mask)
    max_diff = abs_diff.max()
    mean_diff = abs_diff.mean()
    
    print(f"📊 Inference Results:")
    print(f"   PyTorch Time: {pytorch_time:.2f}ms")
    print(f"   ONNX Time: {onnx_time:.2f}ms")
    print(f"   Speedup: {pytorch_time/onnx_time:.2f}x")
    print(f"   Max Difference: {max_diff:.6f}")
    print(f"   Mean Difference: {mean_diff:.6f}")
    
    return {
        'pytorch_mask': pytorch_mask,
        'onnx_mask': onnx_mask,
        'pytorch_time': pytorch_time,
        'onnx_time': onnx_time,
        'max_diff': max_diff,
        'mean_diff': mean_diff
    }

def create_comparison_visualization(original_image, results, image_path):
    """Create visualization comparing PyTorch and ONNX results"""
    print("🎨 Creating comparison visualization...")
    
    pytorch_mask = results['pytorch_mask']
    onnx_mask = results['onnx_mask']
    
    # Resize masks back to original image size
    original_h, original_w = original_image.shape[:2]
    pytorch_resized = cv2.resize(pytorch_mask, (original_w, original_h))
    onnx_resized = cv2.resize(onnx_mask, (original_w, original_h))
    
    # Create binary masks
    pytorch_binary = (pytorch_resized > 0.5).astype(np.uint8) * 255
    onnx_binary = (onnx_resized > 0.5).astype(np.uint8) * 255
    
    # Create visualization
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f'PyTorch vs Accurate ONNX Comparison\n{os.path.basename(image_path)}', 
                 fontsize=16, fontweight='bold')
    
    # Row 1: PyTorch results
    axes[0, 0].imshow(original_image)
    axes[0, 0].set_title('Original Image', fontweight='bold')
    axes[0, 0].axis('off')
    
    axes[0, 1].imshow(pytorch_resized, cmap='hot')
    axes[0, 1].set_title(f'PyTorch Heatmap\nTime: {results["pytorch_time"]:.2f}ms', fontweight='bold')
    axes[0, 1].axis('off')
    
    # PyTorch overlay
    overlay_pytorch = original_image.copy()
    overlay_pytorch[pytorch_binary > 0] = [255, 0, 0]  # Red
    axes[0, 2].imshow(overlay_pytorch)
    axes[0, 2].set_title('PyTorch Overlay (Red)', fontweight='bold')
    axes[0, 2].axis('off')
    
    # Row 2: ONNX results
    axes[1, 0].imshow(original_image)
    axes[1, 0].set_title('Original Image', fontweight='bold')
    axes[1, 0].axis('off')
    
    axes[1, 1].imshow(onnx_resized, cmap='hot')
    axes[1, 1].set_title(f'ONNX Heatmap\nTime: {results["onnx_time"]:.2f}ms', fontweight='bold')
    axes[1, 1].axis('off')
    
    # ONNX overlay
    overlay_onnx = original_image.copy()
    overlay_onnx[onnx_binary > 0] = [0, 255, 0]  # Green
    axes[1, 2].imshow(overlay_onnx)
    axes[1, 2].set_title('ONNX Overlay (Green)', fontweight='bold')
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    
    # Save visualization
    output_path = f"accurate_onnx_comparison_{os.path.splitext(os.path.basename(image_path))[0]}.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"📸 Visualization saved: {output_path}")
    return output_path

def print_final_assessment(results):
    """Print final assessment of ONNX accuracy"""
    print("\n" + "="*70)
    print("🏆 ACCURATE ONNX MODEL ASSESSMENT")
    print("="*70)
    
    max_diff = results['max_diff']
    mean_diff = results['mean_diff']
    speedup = results['pytorch_time'] / results['onnx_time']
    
    print(f"⚡ PERFORMANCE:")
    print(f"   PyTorch: {results['pytorch_time']:.2f}ms")
    print(f"   ONNX: {results['onnx_time']:.2f}ms")
    print(f"   Speedup: {speedup:.2f}x")
    
    print(f"\n🎯 ACCURACY:")
    print(f"   Max Difference: {max_diff:.6f}")
    print(f"   Mean Difference: {mean_diff:.6f}")
    
    # Assessment
    if max_diff < 0.001:
        accuracy_status = "🏆 EXCELLENT - Production ready!"
        recommendation = "✅ Deploy with confidence"
    elif max_diff < 0.01:
        accuracy_status = "✅ VERY GOOD - Suitable for production"
        recommendation = "✅ Recommended for deployment"
    elif max_diff < 0.05:
        accuracy_status = "✅ GOOD - Acceptable for most uses"
        recommendation = "⚠️ Test thoroughly in your application"
    else:
        accuracy_status = "⚠️ MODERATE - Needs validation"
        recommendation = "⚠️ Validate carefully before production"
    
    print(f"\n📋 OVERALL ASSESSMENT: {accuracy_status}")
    print(f"💡 RECOMMENDATION: {recommendation}")
    
    if speedup > 3 and max_diff < 0.01:
        print("\n🎉 BREAKTHROUGH SUCCESS!")
        print("   ✅ Significant speedup with excellent accuracy")
        print("   ✅ Perfect for production chess applications")
    
    print("="*70)

def main():
    """Main testing function"""
    # Configuration
    pytorch_path = "ultimate_v2_breakthrough/ultimate_v2_breakthrough.pth"
    onnx_path = "ultimate_v2_breakthrough/ultimate_v2_breakthrough_accurate.onnx"
    image_path = r"C:\Users\<USER>\OneDrive\Desktop\New folder (4)\24.jpg"
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Using device: {device}")
    
    try:
        # Load models
        pytorch_model, onnx_session = load_models(pytorch_path, onnx_path, device)
        
        if onnx_session is None:
            return
        
        # Preprocess image
        image_tensor, original_image, original_shape = preprocess_image(image_path)
        
        # Run comparison
        results = run_inference_comparison(pytorch_model, onnx_session, image_tensor, device)
        
        # Create visualization
        viz_path = create_comparison_visualization(original_image, results, image_path)
        
        # Print assessment
        print_final_assessment(results)
        
        print(f"\n📸 Comparison visualization: {viz_path}")
        
    except Exception as e:
        print(f"❌ Testing failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
